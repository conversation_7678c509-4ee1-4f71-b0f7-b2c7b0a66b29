2025-08-04 11:11:24 | INFO     | src.utils.logger:setup_logger:55 | 日志系统初始化完成，级别: INFO
2025-08-04 11:11:24 | INFO     | src.utils.logger:setup_logger:57 | 日志文件: logs/action_detection.log
2025-08-04 11:11:24 | INFO     | __main__:main:148 | ============================================================
2025-08-04 11:11:24 | INFO     | __main__:main:149 | 🚀 YOLOv8实时动作检测系统启动
2025-08-04 11:11:24 | INFO     | __main__:main:150 | ============================================================
2025-08-04 11:11:24 | INFO     | __main__:main:167 | 📹 视频源: 0
2025-08-04 11:11:24 | INFO     | __main__:main:168 | 🤖 模型路径: scripts/models/yolov8s.pt
2025-08-04 11:11:24 | INFO     | __main__:main:169 | 📁 输出目录: output/detected_actions
2025-08-04 11:11:24 | INFO     | __main__:main:170 | 🎯 置信度阈值: 0.5
2025-08-04 11:11:24 | INFO     | __main__:main:171 | 📊 IoU阈值 - 持有: 0.3, 容器: 0.5, 释放: 0.2
2025-08-04 11:11:24 | INFO     | __main__:main:172 | ⏰ 冷却时间: 3.0秒
2025-08-04 11:11:24 | INFO     | src.detection.detector:_get_device:55 | torch.cuda.is_available(): True
2025-08-04 11:11:24 | INFO     | src.detection.detector:_get_device:56 | torch.cuda.device_count(): 1
2025-08-04 11:11:24 | INFO     | src.detection.detector:_get_device:62 | 检测到GPU: NVIDIA GeForce RTX 4060 Laptop GPU (7.6GB)
2025-08-04 11:11:24 | INFO     | src.detection.detector:_get_device:66 | 使用GPU设备: cuda:0
2025-08-04 11:11:24 | INFO     | src.detection.detector:_get_device:88 | 使用设备: 0
2025-08-04 11:11:24 | INFO     | src.detection.detector:_get_device:97 | GPU缓存已清理
2025-08-04 11:11:24 | INFO     | src.detection.detector:_get_device:101 | GPU内存分配策略已设置
2025-08-04 11:11:24 | INFO     | src.detection.detector:_load_model:117 | 加载模型: scripts/models/yolov8s.pt
2025-08-04 11:11:24 | INFO     | src.detection.detector:_load_model:123 | 将模型移动到GPU设备: 0
2025-08-04 11:11:24 | INFO     | src.detection.detector:_load_model:129 | 模型已成功移动到GPU
2025-08-04 11:11:24 | INFO     | src.detection.detector:_load_model:138 | 检测类别: {0: 'person', 1: 'bicycle', 2: 'car', 3: 'motorcycle', 4: 'airplane', 5: 'bus', 6: 'train', 7: 'truck', 8: 'boat', 9: 'traffic light', 10: 'fire hydrant', 11: 'stop sign', 12: 'parking meter', 13: 'bench', 14: 'bird', 15: 'cat', 16: 'dog', 17: 'horse', 18: 'sheep', 19: 'cow', 20: 'elephant', 21: 'bear', 22: 'zebra', 23: 'giraffe', 24: 'backpack', 25: 'umbrella', 26: 'handbag', 27: 'tie', 28: 'suitcase', 29: 'frisbee', 30: 'skis', 31: 'snowboard', 32: 'sports ball', 33: 'kite', 34: 'baseball bat', 35: 'baseball glove', 36: 'skateboard', 37: 'surfboard', 38: 'tennis racket', 39: 'bottle', 40: 'wine glass', 41: 'cup', 42: 'fork', 43: 'knife', 44: 'spoon', 45: 'bowl', 46: 'banana', 47: 'apple', 48: 'sandwich', 49: 'orange', 50: 'broccoli', 51: 'carrot', 52: 'hot dog', 53: 'pizza', 54: 'donut', 55: 'cake', 56: 'chair', 57: 'couch', 58: 'potted plant', 59: 'bed', 60: 'dining table', 61: 'toilet', 62: 'tv', 63: 'laptop', 64: 'mouse', 65: 'remote', 66: 'keyboard', 67: 'cell phone', 68: 'microwave', 69: 'oven', 70: 'toaster', 71: 'sink', 72: 'refrigerator', 73: 'book', 74: 'clock', 75: 'vase', 76: 'scissors', 77: 'teddy bear', 78: 'hair drier', 79: 'toothbrush'}
2025-08-04 11:11:24 | INFO     | src.detection.video_capture:_initialize_capture:39 | 初始化摄像头: 0
2025-08-04 11:11:24 | INFO     | src.detection.video_capture:_log_video_info:74 | 视频信息 - 分辨率: 1280x720, FPS: 10.00
2025-08-04 11:11:24 | INFO     | src.state_machine.action_state_machine:__init__:67 | 状态机初始化完成，初始状态: idle
2025-08-04 11:11:24 | INFO     | src.state_machine.action_detector:__init__:94 | 动作检测器初始化完成
2025-08-04 11:11:24 | INFO     | __main__:main:202 | 🖥️  推理设备: 0
2025-08-04 11:11:24 | INFO     | __main__:main:213 | 💡 控制提示:
2025-08-04 11:11:24 | INFO     | __main__:main:214 |    按 'q' 或 ESC 退出程序
2025-08-04 11:11:24 | INFO     | __main__:main:215 |    按 'r' 重置状态机
2025-08-04 11:11:24 | INFO     | __main__:main:216 |    按 'd' 切换调试模式
2025-08-04 11:11:24 | INFO     | __main__:main:218 | 🔍 开始检测...
2025-08-04 11:11:24 | INFO     | __main__:main:219 | ------------------------------------------------------------
2025-08-04 11:11:24 | INFO     | src.state_machine.action_detector:run:113 | 开始运行动作检测
2025-08-04 11:11:26 | INFO     | src.detection.detector:detect:217 | 检测调试 #1: 原始检测数=0, 过滤后检测数=0, 置信度阈值=0.5
2025-08-04 11:11:26 | INFO     | src.detection.detector:detect:219 | 推理时间: 1081.7ms, 平均: 1081.7ms
2025-08-04 11:11:26 | INFO     | src.detection.detector:detect:224 | GPU内存使用: 74.6MB
2025-08-04 11:11:26 | INFO     | src.detection.detector:detect:217 | 检测调试 #2: 原始检测数=0, 过滤后检测数=0, 置信度阈值=0.5
2025-08-04 11:11:26 | INFO     | src.detection.detector:detect:219 | 推理时间: 5.6ms, 平均: 543.6ms
2025-08-04 11:11:26 | INFO     | src.detection.detector:detect:224 | GPU内存使用: 74.6MB
2025-08-04 11:11:26 | INFO     | src.detection.detector:detect:217 | 检测调试 #3: 原始检测数=0, 过滤后检测数=0, 置信度阈值=0.5
2025-08-04 11:11:26 | INFO     | src.detection.detector:detect:219 | 推理时间: 5.5ms, 平均: 364.3ms
2025-08-04 11:11:26 | INFO     | src.detection.detector:detect:224 | GPU内存使用: 74.6MB
2025-08-04 11:11:26 | INFO     | src.detection.detector:detect:217 | 检测调试 #4: 原始检测数=0, 过滤后检测数=0, 置信度阈值=0.5
2025-08-04 11:11:26 | INFO     | src.detection.detector:detect:219 | 推理时间: 5.9ms, 平均: 274.7ms
2025-08-04 11:11:26 | INFO     | src.detection.detector:detect:224 | GPU内存使用: 74.6MB
2025-08-04 11:11:26 | INFO     | src.detection.detector:detect:217 | 检测调试 #5: 原始检测数=0, 过滤后检测数=0, 置信度阈值=0.5
2025-08-04 11:11:26 | INFO     | src.detection.detector:detect:219 | 推理时间: 7.8ms, 平均: 221.3ms
2025-08-04 11:11:26 | INFO     | src.detection.detector:detect:224 | GPU内存使用: 74.6MB
2025-08-04 11:11:39 | INFO     | src.state_machine.action_detector:run:155 | 检测到键盘中断，正在退出...
2025-08-04 11:11:39 | INFO     | src.detection.video_capture:release:203 | 视频捕获资源已释放
2025-08-04 11:11:39 | INFO     | src.state_machine.action_detector:_cleanup:385 | 检测完成 - 总帧数: 68, 总运行时间: 15.4s, 平均FPS: 4.4, 检测到的动作数: 0
2025-08-04 11:11:39 | INFO     | __main__:main:237 | ============================================================
2025-08-04 11:11:39 | INFO     | __main__:main:238 | 👋 程序结束
2025-08-04 11:11:39 | INFO     | __main__:main:239 | ============================================================
2025-08-04 11:12:45 | INFO     | src.utils.logger:setup_logger:55 | 日志系统初始化完成，级别: INFO
2025-08-04 11:12:45 | INFO     | src.utils.logger:setup_logger:57 | 日志文件: logs/action_detection.log
2025-08-04 11:12:45 | INFO     | __main__:main:148 | ============================================================
2025-08-04 11:12:45 | INFO     | __main__:main:149 | 🚀 YOLOv8实时动作检测系统启动
2025-08-04 11:12:45 | INFO     | __main__:main:150 | ============================================================
2025-08-04 11:12:45 | INFO     | __main__:main:167 | 📹 视频源: 0
2025-08-04 11:12:45 | INFO     | __main__:main:168 | 🤖 模型路径: scripts/models/yolov8s.pt
2025-08-04 11:12:45 | INFO     | __main__:main:169 | 📁 输出目录: output/detected_actions
2025-08-04 11:12:45 | INFO     | __main__:main:170 | 🎯 置信度阈值: 0.5
2025-08-04 11:12:45 | INFO     | __main__:main:171 | 📊 IoU阈值 - 持有: 0.3, 容器: 0.5, 释放: 0.2
2025-08-04 11:12:45 | INFO     | __main__:main:172 | ⏰ 冷却时间: 3.0秒
2025-08-04 11:12:45 | INFO     | src.detection.detector:_get_device:55 | torch.cuda.is_available(): True
2025-08-04 11:12:45 | INFO     | src.detection.detector:_get_device:56 | torch.cuda.device_count(): 1
2025-08-04 11:12:45 | INFO     | src.detection.detector:_get_device:62 | 检测到GPU: NVIDIA GeForce RTX 4060 Laptop GPU (7.6GB)
2025-08-04 11:12:45 | INFO     | src.detection.detector:_get_device:66 | 使用GPU设备: cuda:0
2025-08-04 11:12:45 | INFO     | src.detection.detector:_get_device:88 | 使用设备: 0
2025-08-04 11:12:45 | INFO     | src.detection.detector:_get_device:97 | GPU缓存已清理
2025-08-04 11:12:45 | INFO     | src.detection.detector:_get_device:101 | GPU内存分配策略已设置
2025-08-04 11:12:45 | INFO     | src.detection.detector:_load_model:117 | 加载模型: scripts/models/yolov8s.pt
2025-08-04 11:12:45 | INFO     | src.detection.detector:_load_model:123 | 将模型移动到GPU设备: 0
2025-08-04 11:12:45 | INFO     | src.detection.detector:_load_model:129 | 模型已成功移动到GPU
2025-08-04 11:12:45 | INFO     | src.detection.detector:_load_model:138 | 检测类别: {0: 'person', 1: 'bicycle', 2: 'car', 3: 'motorcycle', 4: 'airplane', 5: 'bus', 6: 'train', 7: 'truck', 8: 'boat', 9: 'traffic light', 10: 'fire hydrant', 11: 'stop sign', 12: 'parking meter', 13: 'bench', 14: 'bird', 15: 'cat', 16: 'dog', 17: 'horse', 18: 'sheep', 19: 'cow', 20: 'elephant', 21: 'bear', 22: 'zebra', 23: 'giraffe', 24: 'backpack', 25: 'umbrella', 26: 'handbag', 27: 'tie', 28: 'suitcase', 29: 'frisbee', 30: 'skis', 31: 'snowboard', 32: 'sports ball', 33: 'kite', 34: 'baseball bat', 35: 'baseball glove', 36: 'skateboard', 37: 'surfboard', 38: 'tennis racket', 39: 'bottle', 40: 'wine glass', 41: 'cup', 42: 'fork', 43: 'knife', 44: 'spoon', 45: 'bowl', 46: 'banana', 47: 'apple', 48: 'sandwich', 49: 'orange', 50: 'broccoli', 51: 'carrot', 52: 'hot dog', 53: 'pizza', 54: 'donut', 55: 'cake', 56: 'chair', 57: 'couch', 58: 'potted plant', 59: 'bed', 60: 'dining table', 61: 'toilet', 62: 'tv', 63: 'laptop', 64: 'mouse', 65: 'remote', 66: 'keyboard', 67: 'cell phone', 68: 'microwave', 69: 'oven', 70: 'toaster', 71: 'sink', 72: 'refrigerator', 73: 'book', 74: 'clock', 75: 'vase', 76: 'scissors', 77: 'teddy bear', 78: 'hair drier', 79: 'toothbrush'}
2025-08-04 11:12:45 | INFO     | src.detection.video_capture:_initialize_capture:39 | 初始化摄像头: 0
2025-08-04 11:12:45 | INFO     | src.detection.video_capture:_log_video_info:74 | 视频信息 - 分辨率: 1280x720, FPS: 10.00
2025-08-04 11:12:45 | INFO     | src.state_machine.action_state_machine:__init__:67 | 状态机初始化完成，初始状态: idle
2025-08-04 11:12:45 | INFO     | src.state_machine.action_detector:__init__:94 | 动作检测器初始化完成
2025-08-04 11:12:45 | INFO     | __main__:main:202 | 🖥️  推理设备: 0
2025-08-04 11:12:45 | INFO     | __main__:main:213 | 💡 控制提示:
2025-08-04 11:12:45 | INFO     | __main__:main:214 |    按 'q' 或 ESC 退出程序
2025-08-04 11:12:45 | INFO     | __main__:main:215 |    按 'r' 重置状态机
2025-08-04 11:12:45 | INFO     | __main__:main:216 |    按 'd' 切换调试模式
2025-08-04 11:12:45 | INFO     | __main__:main:218 | 🔍 开始检测...
2025-08-04 11:12:45 | INFO     | __main__:main:219 | ------------------------------------------------------------
2025-08-04 11:12:45 | INFO     | src.state_machine.action_detector:run:113 | 开始运行动作检测
2025-08-04 11:12:47 | INFO     | src.detection.detector:detect:217 | 检测调试 #1: 原始检测数=0, 过滤后检测数=0, 置信度阈值=0.5
2025-08-04 11:12:47 | INFO     | src.detection.detector:detect:219 | 推理时间: 1193.5ms, 平均: 1193.5ms
2025-08-04 11:12:47 | INFO     | src.detection.detector:detect:224 | GPU内存使用: 74.6MB
2025-08-04 11:12:47 | INFO     | src.detection.detector:detect:217 | 检测调试 #2: 原始检测数=0, 过滤后检测数=0, 置信度阈值=0.5
2025-08-04 11:12:47 | INFO     | src.detection.detector:detect:219 | 推理时间: 5.9ms, 平均: 599.7ms
2025-08-04 11:12:47 | INFO     | src.detection.detector:detect:224 | GPU内存使用: 74.6MB
2025-08-04 11:12:47 | INFO     | src.detection.detector:detect:217 | 检测调试 #3: 原始检测数=0, 过滤后检测数=0, 置信度阈值=0.5
2025-08-04 11:12:47 | INFO     | src.detection.detector:detect:219 | 推理时间: 5.7ms, 平均: 401.7ms
2025-08-04 11:12:47 | INFO     | src.detection.detector:detect:224 | GPU内存使用: 74.6MB
2025-08-04 11:12:47 | INFO     | src.detection.detector:detect:217 | 检测调试 #4: 原始检测数=0, 过滤后检测数=0, 置信度阈值=0.5
2025-08-04 11:12:47 | INFO     | src.detection.detector:detect:219 | 推理时间: 7.0ms, 平均: 303.0ms
2025-08-04 11:12:47 | INFO     | src.detection.detector:detect:224 | GPU内存使用: 74.6MB
2025-08-04 11:12:47 | INFO     | src.detection.detector:detect:217 | 检测调试 #5: 原始检测数=0, 过滤后检测数=0, 置信度阈值=0.5
2025-08-04 11:12:47 | INFO     | src.detection.detector:detect:219 | 推理时间: 9.0ms, 平均: 244.2ms
2025-08-04 11:12:47 | INFO     | src.detection.detector:detect:224 | GPU内存使用: 74.6MB
2025-08-04 11:13:09 | INFO     | src.state_machine.action_detector:run:155 | 检测到键盘中断，正在退出...
2025-08-04 11:13:09 | INFO     | src.detection.video_capture:release:203 | 视频捕获资源已释放
2025-08-04 11:13:09 | INFO     | src.state_machine.action_detector:_cleanup:385 | 检测完成 - 总帧数: 107, 总运行时间: 23.7s, 平均FPS: 4.5, 检测到的动作数: 0
2025-08-04 11:13:09 | INFO     | __main__:main:237 | ============================================================
2025-08-04 11:13:09 | INFO     | __main__:main:238 | 👋 程序结束
2025-08-04 11:13:09 | INFO     | __main__:main:239 | ============================================================
2025-08-04 11:13:12 | INFO     | src.utils.logger:setup_logger:55 | 日志系统初始化完成，级别: INFO
2025-08-04 11:13:12 | INFO     | src.utils.logger:setup_logger:57 | 日志文件: logs/action_detection.log
2025-08-04 11:13:12 | INFO     | __main__:main:148 | ============================================================
2025-08-04 11:13:12 | INFO     | __main__:main:149 | 🚀 YOLOv8实时动作检测系统启动
2025-08-04 11:13:12 | INFO     | __main__:main:150 | ============================================================
2025-08-04 11:13:12 | INFO     | __main__:main:156 | 使用命令行指定的视频文件: data/raw_videos/short_video_for_test.mp4
2025-08-04 11:13:12 | INFO     | __main__:main:167 | 📹 视频源: data/raw_videos/short_video_for_test.mp4
2025-08-04 11:13:12 | INFO     | __main__:main:168 | 🤖 模型路径: scripts/models/yolov8s.pt
2025-08-04 11:13:12 | INFO     | __main__:main:169 | 📁 输出目录: output/detected_actions
2025-08-04 11:13:12 | INFO     | __main__:main:170 | 🎯 置信度阈值: 0.5
2025-08-04 11:13:12 | INFO     | __main__:main:171 | 📊 IoU阈值 - 持有: 0.3, 容器: 0.5, 释放: 0.2
2025-08-04 11:13:12 | INFO     | __main__:main:172 | ⏰ 冷却时间: 3.0秒
2025-08-04 11:13:12 | INFO     | src.detection.detector:_get_device:55 | torch.cuda.is_available(): True
2025-08-04 11:13:12 | INFO     | src.detection.detector:_get_device:56 | torch.cuda.device_count(): 1
2025-08-04 11:13:12 | INFO     | src.detection.detector:_get_device:62 | 检测到GPU: NVIDIA GeForce RTX 4060 Laptop GPU (7.6GB)
2025-08-04 11:13:12 | INFO     | src.detection.detector:_get_device:66 | 使用GPU设备: cuda:0
2025-08-04 11:13:12 | INFO     | src.detection.detector:_get_device:88 | 使用设备: 0
2025-08-04 11:13:12 | INFO     | src.detection.detector:_get_device:97 | GPU缓存已清理
2025-08-04 11:13:13 | INFO     | src.detection.detector:_get_device:101 | GPU内存分配策略已设置
2025-08-04 11:13:13 | INFO     | src.detection.detector:_load_model:117 | 加载模型: scripts/models/yolov8s.pt
2025-08-04 11:13:13 | INFO     | src.detection.detector:_load_model:123 | 将模型移动到GPU设备: 0
2025-08-04 11:13:13 | INFO     | src.detection.detector:_load_model:129 | 模型已成功移动到GPU
2025-08-04 11:13:13 | INFO     | src.detection.detector:_load_model:138 | 检测类别: {0: 'person', 1: 'bicycle', 2: 'car', 3: 'motorcycle', 4: 'airplane', 5: 'bus', 6: 'train', 7: 'truck', 8: 'boat', 9: 'traffic light', 10: 'fire hydrant', 11: 'stop sign', 12: 'parking meter', 13: 'bench', 14: 'bird', 15: 'cat', 16: 'dog', 17: 'horse', 18: 'sheep', 19: 'cow', 20: 'elephant', 21: 'bear', 22: 'zebra', 23: 'giraffe', 24: 'backpack', 25: 'umbrella', 26: 'handbag', 27: 'tie', 28: 'suitcase', 29: 'frisbee', 30: 'skis', 31: 'snowboard', 32: 'sports ball', 33: 'kite', 34: 'baseball bat', 35: 'baseball glove', 36: 'skateboard', 37: 'surfboard', 38: 'tennis racket', 39: 'bottle', 40: 'wine glass', 41: 'cup', 42: 'fork', 43: 'knife', 44: 'spoon', 45: 'bowl', 46: 'banana', 47: 'apple', 48: 'sandwich', 49: 'orange', 50: 'broccoli', 51: 'carrot', 52: 'hot dog', 53: 'pizza', 54: 'donut', 55: 'cake', 56: 'chair', 57: 'couch', 58: 'potted plant', 59: 'bed', 60: 'dining table', 61: 'toilet', 62: 'tv', 63: 'laptop', 64: 'mouse', 65: 'remote', 66: 'keyboard', 67: 'cell phone', 68: 'microwave', 69: 'oven', 70: 'toaster', 71: 'sink', 72: 'refrigerator', 73: 'book', 74: 'clock', 75: 'vase', 76: 'scissors', 77: 'teddy bear', 78: 'hair drier', 79: 'toothbrush'}
2025-08-04 11:13:13 | INFO     | src.detection.video_capture:_initialize_capture:52 | 初始化视频文件: data/raw_videos/short_video_for_test.mp4
2025-08-04 11:13:13 | INFO     | src.detection.video_capture:_log_video_info:74 | 视频信息 - 分辨率: 1442x898, FPS: 25.02
2025-08-04 11:13:13 | INFO     | src.detection.video_capture:_log_video_info:79 | 视频时长: 60.36秒, 总帧数: 1510
2025-08-04 11:13:13 | INFO     | src.state_machine.action_state_machine:__init__:67 | 状态机初始化完成，初始状态: idle
2025-08-04 11:13:13 | INFO     | src.state_machine.action_detector:__init__:94 | 动作检测器初始化完成
2025-08-04 11:13:13 | INFO     | __main__:main:202 | 🖥️  推理设备: 0
2025-08-04 11:13:13 | INFO     | __main__:main:213 | 💡 控制提示:
2025-08-04 11:13:13 | INFO     | __main__:main:214 |    按 'q' 或 ESC 退出程序
2025-08-04 11:13:13 | INFO     | __main__:main:215 |    按 'r' 重置状态机
2025-08-04 11:13:13 | INFO     | __main__:main:216 |    按 'd' 切换调试模式
2025-08-04 11:13:13 | INFO     | __main__:main:218 | 🔍 开始检测...
2025-08-04 11:13:13 | INFO     | __main__:main:219 | ------------------------------------------------------------
2025-08-04 11:13:13 | INFO     | src.state_machine.action_detector:run:113 | 开始运行动作检测
2025-08-04 11:13:14 | INFO     | src.detection.detector:detect:217 | 检测调试 #1: 原始检测数=2, 过滤后检测数=0, 置信度阈值=0.5
2025-08-04 11:13:14 | INFO     | src.detection.detector:detect:219 | 推理时间: 1016.2ms, 平均: 1016.2ms
2025-08-04 11:13:14 | INFO     | src.detection.detector:detect:224 | GPU内存使用: 74.6MB
2025-08-04 11:13:14 | INFO     | src.detection.detector:detect:227 | 原始类别: [0.0, 69.0]
2025-08-04 11:13:14 | INFO     | src.detection.detector:detect:228 | 原始置信度: [0.9184210300445557, 0.5974879264831543]
2025-08-04 11:13:14 | INFO     | src.detection.detector:detect:217 | 检测调试 #2: 原始检测数=2, 过滤后检测数=0, 置信度阈值=0.5
2025-08-04 11:13:14 | INFO     | src.detection.detector:detect:219 | 推理时间: 6.9ms, 平均: 511.6ms
2025-08-04 11:13:14 | INFO     | src.detection.detector:detect:224 | GPU内存使用: 74.6MB
2025-08-04 11:13:14 | INFO     | src.detection.detector:detect:227 | 原始类别: [0.0, 69.0]
2025-08-04 11:13:14 | INFO     | src.detection.detector:detect:228 | 原始置信度: [0.9065991640090942, 0.6765909194946289]
2025-08-04 11:13:14 | INFO     | src.detection.detector:detect:217 | 检测调试 #3: 原始检测数=2, 过滤后检测数=0, 置信度阈值=0.5
2025-08-04 11:13:14 | INFO     | src.detection.detector:detect:219 | 推理时间: 7.5ms, 平均: 343.5ms
2025-08-04 11:13:14 | INFO     | src.detection.detector:detect:224 | GPU内存使用: 74.6MB
2025-08-04 11:13:14 | INFO     | src.detection.detector:detect:227 | 原始类别: [0.0, 69.0]
2025-08-04 11:13:14 | INFO     | src.detection.detector:detect:228 | 原始置信度: [0.9199779033660889, 0.6526250243186951]
2025-08-04 11:13:14 | INFO     | src.detection.detector:detect:217 | 检测调试 #4: 原始检测数=2, 过滤后检测数=0, 置信度阈值=0.5
2025-08-04 11:13:14 | INFO     | src.detection.detector:detect:219 | 推理时间: 6.3ms, 平均: 259.2ms
2025-08-04 11:13:14 | INFO     | src.detection.detector:detect:224 | GPU内存使用: 74.6MB
2025-08-04 11:13:14 | INFO     | src.detection.detector:detect:227 | 原始类别: [0.0, 69.0]
2025-08-04 11:13:14 | INFO     | src.detection.detector:detect:228 | 原始置信度: [0.9236124753952026, 0.5702254772186279]
2025-08-04 11:13:14 | INFO     | src.detection.detector:detect:217 | 检测调试 #5: 原始检测数=2, 过滤后检测数=0, 置信度阈值=0.5
2025-08-04 11:13:14 | INFO     | src.detection.detector:detect:219 | 推理时间: 6.6ms, 平均: 208.7ms
2025-08-04 11:13:14 | INFO     | src.detection.detector:detect:224 | GPU内存使用: 74.6MB
2025-08-04 11:13:14 | INFO     | src.detection.detector:detect:227 | 原始类别: [0.0, 69.0]
2025-08-04 11:13:14 | INFO     | src.detection.detector:detect:228 | 原始置信度: [0.9197899103164673, 0.5257490277290344]
2025-08-04 11:13:24 | INFO     | src.state_machine.action_detector:_log_statistics:348 | 统计信息 - 帧数: 300, 运行时间: 11.1s, FPS: 27.0, 状态: idle, 动作数: 0
2025-08-04 11:13:24 | INFO     | src.state_machine.action_detector:_log_statistics:356 | 检测统计 - 有检测的帧: 0/300 (0.0%), 总检测数: 0, 手: 0, 产品: 0, 容器: 0
2025-08-04 11:13:24 | INFO     | src.state_machine.action_detector:_log_statistics:365 | 性能统计 - 平均推理时间: 16.1ms, 估计FPS: 62.0, 设备: 0
2025-08-04 11:13:24 | INFO     | src.state_machine.action_detector:_log_statistics:370 | GPU内存 - 已分配: 74.6MB, 已缓存: 128.0MB
2025-08-04 11:13:27 | INFO     | src.state_machine.action_detector:run:155 | 检测到键盘中断，正在退出...
2025-08-04 11:13:27 | INFO     | src.detection.video_capture:release:203 | 视频捕获资源已释放
2025-08-04 11:13:27 | INFO     | src.state_machine.action_detector:_cleanup:385 | 检测完成 - 总帧数: 409, 总运行时间: 14.8s, 平均FPS: 27.7, 检测到的动作数: 0
2025-08-04 11:13:27 | INFO     | __main__:main:237 | ============================================================
2025-08-04 11:13:27 | INFO     | __main__:main:238 | 👋 程序结束
2025-08-04 11:13:27 | INFO     | __main__:main:239 | ============================================================
2025-08-04 13:45:01 | INFO     | src.utils.logger:setup_logger:55 | 日志系统初始化完成，级别: INFO
2025-08-04 13:45:01 | INFO     | src.utils.logger:setup_logger:57 | 日志文件: logs/action_detection.log
2025-08-04 13:45:01 | INFO     | __main__:main:148 | ============================================================
2025-08-04 13:45:01 | INFO     | __main__:main:149 | 🚀 YOLOv8实时动作检测系统启动
2025-08-04 13:45:01 | INFO     | __main__:main:150 | ============================================================
2025-08-04 13:45:01 | INFO     | __main__:main:156 | 使用命令行指定的视频文件: data/raw_videos/short_video_for_test.mp4
2025-08-04 13:45:01 | INFO     | __main__:main:167 | 📹 视频源: data/raw_videos/short_video_for_test.mp4
2025-08-04 13:45:01 | INFO     | __main__:main:168 | 🤖 模型路径: scripts/models/yolov8s.pt
2025-08-04 13:45:01 | INFO     | __main__:main:169 | 📁 输出目录: output/detected_actions
2025-08-04 13:45:01 | INFO     | __main__:main:170 | 🎯 置信度阈值: 0.5
2025-08-04 13:45:01 | INFO     | __main__:main:171 | 📊 IoU阈值 - 持有: 0.3, 容器: 0.5, 释放: 0.2
2025-08-04 13:45:01 | INFO     | __main__:main:172 | ⏰ 冷却时间: 3.0秒
2025-08-04 13:45:01 | INFO     | src.detection.detector:_get_device:55 | torch.cuda.is_available(): True
2025-08-04 13:45:01 | INFO     | src.detection.detector:_get_device:56 | torch.cuda.device_count(): 1
2025-08-04 13:45:01 | INFO     | src.detection.detector:_get_device:62 | 检测到GPU: NVIDIA GeForce RTX 4060 Laptop GPU (7.6GB)
2025-08-04 13:45:01 | INFO     | src.detection.detector:_get_device:66 | 使用GPU设备: cuda:0
2025-08-04 13:45:01 | INFO     | src.detection.detector:_get_device:88 | 使用设备: 0
2025-08-04 13:45:01 | INFO     | src.detection.detector:_get_device:97 | GPU缓存已清理
2025-08-04 13:45:01 | INFO     | src.detection.detector:_get_device:101 | GPU内存分配策略已设置
2025-08-04 13:45:01 | INFO     | src.detection.detector:_load_model:117 | 加载模型: scripts/models/yolov8s.pt
2025-08-04 13:45:01 | INFO     | src.detection.detector:_load_model:123 | 将模型移动到GPU设备: 0
2025-08-04 13:45:01 | INFO     | src.detection.detector:_load_model:129 | 模型已成功移动到GPU
2025-08-04 13:45:01 | INFO     | src.detection.detector:_load_model:138 | 检测类别: {0: 'person', 1: 'bicycle', 2: 'car', 3: 'motorcycle', 4: 'airplane', 5: 'bus', 6: 'train', 7: 'truck', 8: 'boat', 9: 'traffic light', 10: 'fire hydrant', 11: 'stop sign', 12: 'parking meter', 13: 'bench', 14: 'bird', 15: 'cat', 16: 'dog', 17: 'horse', 18: 'sheep', 19: 'cow', 20: 'elephant', 21: 'bear', 22: 'zebra', 23: 'giraffe', 24: 'backpack', 25: 'umbrella', 26: 'handbag', 27: 'tie', 28: 'suitcase', 29: 'frisbee', 30: 'skis', 31: 'snowboard', 32: 'sports ball', 33: 'kite', 34: 'baseball bat', 35: 'baseball glove', 36: 'skateboard', 37: 'surfboard', 38: 'tennis racket', 39: 'bottle', 40: 'wine glass', 41: 'cup', 42: 'fork', 43: 'knife', 44: 'spoon', 45: 'bowl', 46: 'banana', 47: 'apple', 48: 'sandwich', 49: 'orange', 50: 'broccoli', 51: 'carrot', 52: 'hot dog', 53: 'pizza', 54: 'donut', 55: 'cake', 56: 'chair', 57: 'couch', 58: 'potted plant', 59: 'bed', 60: 'dining table', 61: 'toilet', 62: 'tv', 63: 'laptop', 64: 'mouse', 65: 'remote', 66: 'keyboard', 67: 'cell phone', 68: 'microwave', 69: 'oven', 70: 'toaster', 71: 'sink', 72: 'refrigerator', 73: 'book', 74: 'clock', 75: 'vase', 76: 'scissors', 77: 'teddy bear', 78: 'hair drier', 79: 'toothbrush'}
2025-08-04 13:45:01 | INFO     | src.detection.video_capture:_initialize_capture:52 | 初始化视频文件: data/raw_videos/short_video_for_test.mp4
2025-08-04 13:45:01 | INFO     | src.detection.video_capture:_log_video_info:74 | 视频信息 - 分辨率: 1442x898, FPS: 25.02
2025-08-04 13:45:01 | INFO     | src.detection.video_capture:_log_video_info:79 | 视频时长: 60.36秒, 总帧数: 1510
2025-08-04 13:45:01 | INFO     | src.state_machine.action_state_machine:__init__:67 | 状态机初始化完成，初始状态: idle
2025-08-04 13:45:01 | INFO     | src.state_machine.action_detector:__init__:94 | 动作检测器初始化完成
2025-08-04 13:45:01 | INFO     | __main__:main:202 | 🖥️  推理设备: 0
2025-08-04 13:45:01 | INFO     | __main__:main:213 | 💡 控制提示:
2025-08-04 13:45:01 | INFO     | __main__:main:214 |    按 'q' 或 ESC 退出程序
2025-08-04 13:45:01 | INFO     | __main__:main:215 |    按 'r' 重置状态机
2025-08-04 13:45:01 | INFO     | __main__:main:216 |    按 'd' 切换调试模式
2025-08-04 13:45:01 | INFO     | __main__:main:218 | 🔍 开始检测...
2025-08-04 13:45:01 | INFO     | __main__:main:219 | ------------------------------------------------------------
2025-08-04 13:45:01 | INFO     | src.state_machine.action_detector:run:113 | 开始运行动作检测
2025-08-04 13:45:02 | INFO     | src.detection.detector:detect:217 | 检测调试 #1: 原始检测数=2, 过滤后检测数=0, 置信度阈值=0.5
2025-08-04 13:45:02 | INFO     | src.detection.detector:detect:219 | 推理时间: 1553.2ms, 平均: 1553.2ms
2025-08-04 13:45:02 | INFO     | src.detection.detector:detect:224 | GPU内存使用: 74.6MB
2025-08-04 13:45:02 | INFO     | src.detection.detector:detect:227 | 原始类别: [0.0, 69.0]
2025-08-04 13:45:02 | INFO     | src.detection.detector:detect:228 | 原始置信度: [0.9184210300445557, 0.5974879264831543]
2025-08-04 13:45:03 | INFO     | src.detection.detector:detect:217 | 检测调试 #2: 原始检测数=2, 过滤后检测数=0, 置信度阈值=0.5
2025-08-04 13:45:03 | INFO     | src.detection.detector:detect:219 | 推理时间: 7.4ms, 平均: 780.3ms
2025-08-04 13:45:03 | INFO     | src.detection.detector:detect:224 | GPU内存使用: 74.6MB
2025-08-04 13:45:03 | INFO     | src.detection.detector:detect:227 | 原始类别: [0.0, 69.0]
2025-08-04 13:45:03 | INFO     | src.detection.detector:detect:228 | 原始置信度: [0.9065991640090942, 0.6765909194946289]
2025-08-04 13:45:03 | INFO     | src.detection.detector:detect:217 | 检测调试 #3: 原始检测数=2, 过滤后检测数=0, 置信度阈值=0.5
2025-08-04 13:45:03 | INFO     | src.detection.detector:detect:219 | 推理时间: 7.4ms, 平均: 522.7ms
2025-08-04 13:45:03 | INFO     | src.detection.detector:detect:224 | GPU内存使用: 74.6MB
2025-08-04 13:45:03 | INFO     | src.detection.detector:detect:227 | 原始类别: [0.0, 69.0]
2025-08-04 13:45:03 | INFO     | src.detection.detector:detect:228 | 原始置信度: [0.9199779033660889, 0.6526250243186951]
2025-08-04 13:45:03 | INFO     | src.detection.detector:detect:217 | 检测调试 #4: 原始检测数=2, 过滤后检测数=0, 置信度阈值=0.5
2025-08-04 13:45:03 | INFO     | src.detection.detector:detect:219 | 推理时间: 8.2ms, 平均: 394.1ms
2025-08-04 13:45:03 | INFO     | src.detection.detector:detect:224 | GPU内存使用: 74.6MB
2025-08-04 13:45:03 | INFO     | src.detection.detector:detect:227 | 原始类别: [0.0, 69.0]
2025-08-04 13:45:03 | INFO     | src.detection.detector:detect:228 | 原始置信度: [0.9236124753952026, 0.5702254772186279]
2025-08-04 13:45:03 | INFO     | src.detection.detector:detect:217 | 检测调试 #5: 原始检测数=2, 过滤后检测数=0, 置信度阈值=0.5
2025-08-04 13:45:03 | INFO     | src.detection.detector:detect:219 | 推理时间: 10.1ms, 平均: 317.3ms
2025-08-04 13:45:03 | INFO     | src.detection.detector:detect:224 | GPU内存使用: 74.6MB
2025-08-04 13:45:03 | INFO     | src.detection.detector:detect:227 | 原始类别: [0.0, 69.0]
2025-08-04 13:45:03 | INFO     | src.detection.detector:detect:228 | 原始置信度: [0.9197899103164673, 0.5257490277290344]
2025-08-04 13:45:13 | INFO     | src.state_machine.action_detector:_log_statistics:348 | 统计信息 - 帧数: 300, 运行时间: 11.7s, FPS: 25.6, 状态: idle, 动作数: 0
2025-08-04 13:45:13 | INFO     | src.state_machine.action_detector:_log_statistics:356 | 检测统计 - 有检测的帧: 0/300 (0.0%), 总检测数: 0, 手: 0, 产品: 0, 容器: 0
2025-08-04 13:45:13 | INFO     | src.state_machine.action_detector:_log_statistics:365 | 性能统计 - 平均推理时间: 15.7ms, 估计FPS: 63.7, 设备: 0
2025-08-04 13:45:13 | INFO     | src.state_machine.action_detector:_log_statistics:370 | GPU内存 - 已分配: 74.6MB, 已缓存: 128.0MB
2025-08-04 13:45:23 | INFO     | src.state_machine.action_detector:_log_statistics:348 | 统计信息 - 帧数: 600, 运行时间: 21.8s, FPS: 27.5, 状态: idle, 动作数: 0
2025-08-04 13:45:23 | INFO     | src.state_machine.action_detector:_log_statistics:356 | 检测统计 - 有检测的帧: 0/600 (0.0%), 总检测数: 0, 手: 0, 产品: 0, 容器: 0
2025-08-04 13:45:23 | INFO     | src.state_machine.action_detector:_log_statistics:365 | 性能统计 - 平均推理时间: 14.3ms, 估计FPS: 70.0, 设备: 0
2025-08-04 13:45:23 | INFO     | src.state_machine.action_detector:_log_statistics:370 | GPU内存 - 已分配: 74.6MB, 已缓存: 128.0MB
2025-08-04 13:45:33 | INFO     | src.state_machine.action_detector:_log_statistics:348 | 统计信息 - 帧数: 900, 运行时间: 31.8s, FPS: 28.3, 状态: idle, 动作数: 0
2025-08-04 13:45:33 | INFO     | src.state_machine.action_detector:_log_statistics:356 | 检测统计 - 有检测的帧: 0/900 (0.0%), 总检测数: 0, 手: 0, 产品: 0, 容器: 0
2025-08-04 13:45:33 | INFO     | src.state_machine.action_detector:_log_statistics:365 | 性能统计 - 平均推理时间: 14.0ms, 估计FPS: 71.4, 设备: 0
2025-08-04 13:45:33 | INFO     | src.state_machine.action_detector:_log_statistics:370 | GPU内存 - 已分配: 74.6MB, 已缓存: 128.0MB
2025-08-04 13:45:43 | INFO     | src.state_machine.action_detector:_log_statistics:348 | 统计信息 - 帧数: 1200, 运行时间: 41.9s, FPS: 28.7, 状态: idle, 动作数: 0
2025-08-04 13:45:43 | INFO     | src.state_machine.action_detector:_log_statistics:356 | 检测统计 - 有检测的帧: 0/1200 (0.0%), 总检测数: 0, 手: 0, 产品: 0, 容器: 0
2025-08-04 13:45:43 | INFO     | src.state_machine.action_detector:_log_statistics:365 | 性能统计 - 平均推理时间: 13.9ms, 估计FPS: 72.1, 设备: 0
2025-08-04 13:45:43 | INFO     | src.state_machine.action_detector:_log_statistics:370 | GPU内存 - 已分配: 74.6MB, 已缓存: 128.0MB
2025-08-04 13:45:53 | INFO     | src.state_machine.action_detector:_log_statistics:348 | 统计信息 - 帧数: 1500, 运行时间: 51.9s, FPS: 28.9, 状态: idle, 动作数: 0
2025-08-04 13:45:53 | INFO     | src.state_machine.action_detector:_log_statistics:356 | 检测统计 - 有检测的帧: 0/1500 (0.0%), 总检测数: 0, 手: 0, 产品: 0, 容器: 0
2025-08-04 13:45:53 | INFO     | src.state_machine.action_detector:_log_statistics:365 | 性能统计 - 平均推理时间: 13.8ms, 估计FPS: 72.3, 设备: 0
2025-08-04 13:45:53 | INFO     | src.state_machine.action_detector:_log_statistics:370 | GPU内存 - 已分配: 74.6MB, 已缓存: 128.0MB
2025-08-04 13:45:53 | INFO     | src.detection.video_capture:get_frame_generator:127 | 视频文件读取完毕
2025-08-04 13:45:53 | INFO     | src.detection.video_capture:release:203 | 视频捕获资源已释放
2025-08-04 13:45:53 | INFO     | src.state_machine.action_detector:_cleanup:385 | 检测完成 - 总帧数: 1509, 总运行时间: 52.2s, 平均FPS: 28.9, 检测到的动作数: 0
2025-08-04 13:45:53 | INFO     | __main__:main:237 | ============================================================
2025-08-04 13:45:53 | INFO     | __main__:main:238 | 👋 程序结束
2025-08-04 13:45:53 | INFO     | __main__:main:239 | ============================================================
2025-08-04 16:59:48 | INFO     | src.utils.logger:setup_logger:55 | 日志系统初始化完成，级别: INFO
2025-08-04 16:59:48 | INFO     | src.utils.logger:setup_logger:57 | 日志文件: logs/action_detection.log
2025-08-04 16:59:48 | INFO     | __main__:main:170 | ============================================================
2025-08-04 16:59:48 | INFO     | __main__:main:171 | 🚀 YOLOv8实时动作检测系统启动
2025-08-04 16:59:48 | INFO     | __main__:main:172 | ============================================================
2025-08-04 16:59:48 | INFO     | __main__:main:178 | 使用命令行指定的视频文件: /home/<USER>/wolong_ws/yolo_detect_ws/data/test_videos/short_video_for_test.mp4
2025-08-04 16:59:48 | INFO     | __main__:main:189 | 📹 视频源: /home/<USER>/wolong_ws/yolo_detect_ws/data/test_videos/short_video_for_test.mp4
2025-08-04 16:59:48 | INFO     | __main__:main:190 | 🤖 模型路径: yolo_training/yolov8n_custom2/weights/best.pt
2025-08-04 16:59:48 | INFO     | __main__:main:191 | 📁 输出目录: output/detected_actions
2025-08-04 16:59:48 | INFO     | __main__:main:192 | 🎯 置信度阈值: 0.5
2025-08-04 16:59:48 | INFO     | __main__:main:193 | 📊 交互阈值 - IoU: 0.1, 距离: 50px
2025-08-04 16:59:48 | INFO     | __main__:main:194 | ⏰ 冷却时间: 3.0秒, 动作持续时间: 0.5秒
2025-08-04 16:59:48 | INFO     | src.detection.detector:_get_device:55 | torch.cuda.is_available(): True
2025-08-04 16:59:48 | INFO     | src.detection.detector:_get_device:56 | torch.cuda.device_count(): 1
2025-08-04 16:59:48 | INFO     | src.detection.detector:_get_device:62 | 检测到GPU: NVIDIA GeForce RTX 4060 Laptop GPU (7.6GB)
2025-08-04 16:59:48 | INFO     | src.detection.detector:_get_device:66 | 使用GPU设备: cuda:0
2025-08-04 16:59:48 | INFO     | src.detection.detector:_get_device:88 | 使用设备: 0
2025-08-04 16:59:48 | INFO     | src.detection.detector:_get_device:97 | GPU缓存已清理
2025-08-04 16:59:48 | INFO     | src.detection.detector:_get_device:101 | GPU内存分配策略已设置
2025-08-04 16:59:48 | INFO     | src.detection.detector:_load_model:117 | 加载模型: yolo_training/yolov8n_custom2/weights/best.pt
2025-08-04 16:59:48 | INFO     | src.detection.detector:_load_model:123 | 将模型移动到GPU设备: 0
2025-08-04 16:59:48 | INFO     | src.detection.detector:_load_model:129 | 模型已成功移动到GPU
2025-08-04 16:59:48 | INFO     | src.detection.detector:_load_model:138 | 检测类别: {0: 'blue_box', 1: 'upper_yellow_box', 2: 'lower_yellow_box', 3: 'hand'}
2025-08-04 16:59:48 | INFO     | src.detection.video_capture:_initialize_capture:52 | 初始化视频文件: /home/<USER>/wolong_ws/yolo_detect_ws/data/test_videos/short_video_for_test.mp4
2025-08-04 16:59:48 | INFO     | src.detection.video_capture:_log_video_info:74 | 视频信息 - 分辨率: 1442x898, FPS: 25.02
2025-08-04 16:59:48 | INFO     | src.detection.video_capture:_log_video_info:79 | 视频时长: 60.36秒, 总帧数: 1510
2025-08-04 16:59:48 | INFO     | src.state_machine.action_state_machine:__init__:73 | 状态机初始化完成，初始状态: idle
2025-08-04 16:59:48 | INFO     | src.utils.video_buffer:__init__:33 | 视频缓存器初始化完成，缓存大小: 31帧，帧率: 30.0fps
2025-08-04 16:59:48 | INFO     | src.utils.video_buffer:__init__:168 | 确保目录存在: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_ok
2025-08-04 16:59:48 | INFO     | src.utils.video_buffer:__init__:168 | 确保目录存在: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_nok_electric
2025-08-04 16:59:48 | INFO     | src.utils.video_buffer:__init__:168 | 确保目录存在: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_nok_appearence
2025-08-04 16:59:48 | INFO     | src.state_machine.action_detector:__init__:96 | 视频保存功能已启用，缓存大小: 31帧
2025-08-04 16:59:48 | INFO     | src.state_machine.action_detector:__init__:120 | 动作检测器初始化完成
2025-08-04 16:59:48 | INFO     | __main__:main:226 | 🖥️  推理设备: 0
2025-08-04 16:59:48 | INFO     | __main__:main:237 | 💡 控制提示:
2025-08-04 16:59:48 | INFO     | __main__:main:238 |    按 'q' 或 ESC 退出程序
2025-08-04 16:59:48 | INFO     | __main__:main:239 |    按 'r' 重置状态机
2025-08-04 16:59:48 | INFO     | __main__:main:240 |    按 'd' 切换调试模式
2025-08-04 16:59:48 | INFO     | __main__:main:242 | 🔍 开始检测...
2025-08-04 16:59:48 | INFO     | __main__:main:243 | ------------------------------------------------------------
2025-08-04 16:59:48 | INFO     | src.state_machine.action_detector:run:139 | 开始运行动作检测
2025-08-04 16:59:49 | INFO     | src.detection.detector:detect:223 | 检测调试 #1: 原始检测数=2, 过滤后检测数=2, 置信度阈值=0.5
2025-08-04 16:59:49 | INFO     | src.detection.detector:detect:225 | 推理时间: 1125.6ms, 平均: 1125.6ms
2025-08-04 16:59:49 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 16:59:49 | INFO     | src.detection.detector:detect:233 | 原始类别: [2.0, 1.0]
2025-08-04 16:59:49 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.8734681606292725, 0.8511121273040771]
2025-08-04 16:59:49 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 1: 检测到 2 个对象
2025-08-04 16:59:49 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 16:59:49 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.851, 位置: [1146, 236, 1421, 522]
2025-08-04 16:59:49 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 16:59:49 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.873, 位置: [1102, 515, 1342, 707]
2025-08-04 16:59:49 | INFO     | src.detection.detector:detect:223 | 检测调试 #2: 原始检测数=2, 过滤后检测数=2, 置信度阈值=0.5
2025-08-04 16:59:49 | INFO     | src.detection.detector:detect:225 | 推理时间: 6.7ms, 平均: 566.2ms
2025-08-04 16:59:49 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 16:59:49 | INFO     | src.detection.detector:detect:233 | 原始类别: [1.0, 2.0]
2025-08-04 16:59:49 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.8752053380012512, 0.870661199092865]
2025-08-04 16:59:49 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 2: 检测到 2 个对象
2025-08-04 16:59:49 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 16:59:49 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.875, 位置: [1146, 237, 1420, 522]
2025-08-04 16:59:49 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 16:59:49 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.871, 位置: [1102, 514, 1342, 706]
2025-08-04 16:59:49 | INFO     | src.detection.detector:detect:223 | 检测调试 #3: 原始检测数=2, 过滤后检测数=2, 置信度阈值=0.5
2025-08-04 16:59:49 | INFO     | src.detection.detector:detect:225 | 推理时间: 6.1ms, 平均: 379.5ms
2025-08-04 16:59:49 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 16:59:49 | INFO     | src.detection.detector:detect:233 | 原始类别: [1.0, 2.0]
2025-08-04 16:59:49 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.8700747489929199, 0.861918032169342]
2025-08-04 16:59:49 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 3: 检测到 2 个对象
2025-08-04 16:59:49 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 16:59:49 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.870, 位置: [1145, 237, 1420, 522]
2025-08-04 16:59:49 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 16:59:49 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.862, 位置: [1102, 515, 1342, 707]
2025-08-04 16:59:49 | INFO     | src.detection.detector:detect:223 | 检测调试 #4: 原始检测数=2, 过滤后检测数=2, 置信度阈值=0.5
2025-08-04 16:59:49 | INFO     | src.detection.detector:detect:225 | 推理时间: 6.0ms, 平均: 286.1ms
2025-08-04 16:59:49 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 16:59:49 | INFO     | src.detection.detector:detect:233 | 原始类别: [1.0, 2.0]
2025-08-04 16:59:49 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.8732701539993286, 0.8566346168518066]
2025-08-04 16:59:49 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 4: 检测到 2 个对象
2025-08-04 16:59:49 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 16:59:49 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.873, 位置: [1145, 237, 1420, 522]
2025-08-04 16:59:49 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 16:59:49 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.857, 位置: [1102, 515, 1342, 707]
2025-08-04 16:59:49 | INFO     | src.detection.detector:detect:223 | 检测调试 #5: 原始检测数=2, 过滤后检测数=2, 置信度阈值=0.5
2025-08-04 16:59:49 | INFO     | src.detection.detector:detect:225 | 推理时间: 5.7ms, 平均: 230.0ms
2025-08-04 16:59:49 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 16:59:49 | INFO     | src.detection.detector:detect:233 | 原始类别: [2.0, 1.0]
2025-08-04 16:59:49 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.8743568062782288, 0.8706481456756592]
2025-08-04 16:59:49 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 5: 检测到 2 个对象
2025-08-04 16:59:49 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 16:59:49 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.871, 位置: [1145, 237, 1420, 522]
2025-08-04 16:59:49 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 16:59:49 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.874, 位置: [1102, 514, 1342, 706]
2025-08-04 16:59:49 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 6: 检测到 2 个对象
2025-08-04 16:59:49 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 16:59:49 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.867, 位置: [1145, 237, 1420, 522]
2025-08-04 16:59:49 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 16:59:49 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.872, 位置: [1102, 514, 1342, 706]
2025-08-04 16:59:49 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 7: 检测到 2 个对象
2025-08-04 16:59:49 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 16:59:49 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.864, 位置: [1145, 237, 1420, 522]
2025-08-04 16:59:49 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 16:59:49 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.871, 位置: [1102, 514, 1342, 706]
2025-08-04 16:59:49 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 8: 检测到 2 个对象
2025-08-04 16:59:49 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 16:59:49 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.862, 位置: [1145, 237, 1420, 522]
2025-08-04 16:59:49 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 16:59:49 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.868, 位置: [1102, 514, 1342, 706]
2025-08-04 16:59:49 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 9: 检测到 2 个对象
2025-08-04 16:59:49 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 16:59:49 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.869, 位置: [1145, 237, 1420, 522]
2025-08-04 16:59:49 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 16:59:49 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.864, 位置: [1102, 514, 1342, 706]
2025-08-04 16:59:49 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 10: 检测到 2 个对象
2025-08-04 16:59:49 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 16:59:49 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.874, 位置: [1146, 237, 1420, 522]
2025-08-04 16:59:49 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 16:59:49 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.868, 位置: [1102, 514, 1342, 706]
2025-08-04 16:59:52 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 100: 检测到 2 个对象
2025-08-04 16:59:52 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 16:59:52 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.900, 位置: [1145, 237, 1420, 522]
2025-08-04 16:59:52 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 16:59:52 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.873, 位置: [1102, 514, 1342, 706]
2025-08-04 16:59:56 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 200: 检测到 2 个对象
2025-08-04 16:59:56 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 16:59:56 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.886, 位置: [1147, 236, 1420, 522]
2025-08-04 16:59:56 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 16:59:56 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.860, 位置: [1102, 513, 1342, 707]
2025-08-04 16:59:59 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 300: 检测到 2 个对象
2025-08-04 16:59:59 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 16:59:59 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.902, 位置: [1147, 236, 1419, 522]
2025-08-04 16:59:59 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 16:59:59 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.845, 位置: [1102, 513, 1343, 707]
2025-08-04 16:59:59 | INFO     | src.state_machine.action_detector:_log_statistics:396 | 统计信息 - 帧数: 300, 运行时间: 11.2s, FPS: 26.8, 状态: idle, 动作数: 0
2025-08-04 16:59:59 | ERROR    | src.state_machine.action_detector:run:188 | 运行过程中出错: 'product_detections'
2025-08-04 16:59:59 | INFO     | src.detection.video_capture:release:203 | 视频捕获资源已释放
2025-08-04 16:59:59 | INFO     | src.state_machine.action_detector:_cleanup:433 | 检测完成 - 总帧数: 300, 总运行时间: 11.2s, 平均FPS: 26.8, 检测到的动作数: 0
2025-08-04 16:59:59 | INFO     | __main__:main:261 | ============================================================
2025-08-04 16:59:59 | INFO     | __main__:main:262 | 👋 程序结束
2025-08-04 16:59:59 | INFO     | __main__:main:263 | ============================================================
2025-08-04 17:03:32 | INFO     | src.utils.logger:setup_logger:55 | 日志系统初始化完成，级别: INFO
2025-08-04 17:03:32 | INFO     | src.utils.logger:setup_logger:57 | 日志文件: logs/action_detection.log
2025-08-04 17:03:32 | INFO     | __main__:main:170 | ============================================================
2025-08-04 17:03:32 | INFO     | __main__:main:171 | 🚀 YOLOv8实时动作检测系统启动
2025-08-04 17:03:32 | INFO     | __main__:main:172 | ============================================================
2025-08-04 17:03:32 | INFO     | __main__:main:178 | 使用命令行指定的视频文件: /home/<USER>/wolong_ws/yolo_detect_ws/data/test_videos/short_video_for_test.mp4
2025-08-04 17:03:32 | INFO     | __main__:main:189 | 📹 视频源: /home/<USER>/wolong_ws/yolo_detect_ws/data/test_videos/short_video_for_test.mp4
2025-08-04 17:03:32 | INFO     | __main__:main:190 | 🤖 模型路径: yolo_training/yolov8n_custom2/weights/best.pt
2025-08-04 17:03:32 | INFO     | __main__:main:191 | 📁 输出目录: output/detected_actions
2025-08-04 17:03:32 | INFO     | __main__:main:192 | 🎯 置信度阈值: 0.5
2025-08-04 17:03:32 | INFO     | __main__:main:193 | 📊 交互阈值 - IoU: 0.1, 距离: 50px
2025-08-04 17:03:32 | INFO     | __main__:main:194 | ⏰ 冷却时间: 3.0秒, 动作持续时间: 0.5秒
2025-08-04 17:03:32 | INFO     | src.detection.detector:_get_device:55 | torch.cuda.is_available(): True
2025-08-04 17:03:32 | INFO     | src.detection.detector:_get_device:56 | torch.cuda.device_count(): 1
2025-08-04 17:03:32 | INFO     | src.detection.detector:_get_device:62 | 检测到GPU: NVIDIA GeForce RTX 4060 Laptop GPU (7.6GB)
2025-08-04 17:03:32 | INFO     | src.detection.detector:_get_device:66 | 使用GPU设备: cuda:0
2025-08-04 17:03:32 | INFO     | src.detection.detector:_get_device:88 | 使用设备: 0
2025-08-04 17:03:32 | INFO     | src.detection.detector:_get_device:97 | GPU缓存已清理
2025-08-04 17:03:32 | INFO     | src.detection.detector:_get_device:101 | GPU内存分配策略已设置
2025-08-04 17:03:32 | INFO     | src.detection.detector:_load_model:117 | 加载模型: yolo_training/yolov8n_custom2/weights/best.pt
2025-08-04 17:03:32 | INFO     | src.detection.detector:_load_model:123 | 将模型移动到GPU设备: 0
2025-08-04 17:03:32 | INFO     | src.detection.detector:_load_model:129 | 模型已成功移动到GPU
2025-08-04 17:03:32 | INFO     | src.detection.detector:_load_model:138 | 检测类别: {0: 'blue_box', 1: 'upper_yellow_box', 2: 'lower_yellow_box', 3: 'hand'}
2025-08-04 17:03:32 | INFO     | src.detection.video_capture:_initialize_capture:52 | 初始化视频文件: /home/<USER>/wolong_ws/yolo_detect_ws/data/test_videos/short_video_for_test.mp4
2025-08-04 17:03:32 | INFO     | src.detection.video_capture:_log_video_info:74 | 视频信息 - 分辨率: 1442x898, FPS: 25.02
2025-08-04 17:03:32 | INFO     | src.detection.video_capture:_log_video_info:79 | 视频时长: 60.36秒, 总帧数: 1510
2025-08-04 17:03:32 | INFO     | src.state_machine.action_state_machine:__init__:73 | 状态机初始化完成，初始状态: idle
2025-08-04 17:03:32 | INFO     | src.utils.video_buffer:__init__:33 | 视频缓存器初始化完成，缓存大小: 31帧，帧率: 30.0fps
2025-08-04 17:03:32 | INFO     | src.utils.video_buffer:__init__:168 | 确保目录存在: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_ok
2025-08-04 17:03:32 | INFO     | src.utils.video_buffer:__init__:168 | 确保目录存在: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_nok_electric
2025-08-04 17:03:32 | INFO     | src.utils.video_buffer:__init__:168 | 确保目录存在: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_nok_appearence
2025-08-04 17:03:32 | INFO     | src.state_machine.action_detector:__init__:96 | 视频保存功能已启用，缓存大小: 31帧
2025-08-04 17:03:32 | INFO     | src.state_machine.action_detector:__init__:120 | 动作检测器初始化完成
2025-08-04 17:03:32 | INFO     | __main__:main:226 | 🖥️  推理设备: 0
2025-08-04 17:03:32 | INFO     | __main__:main:237 | 💡 控制提示:
2025-08-04 17:03:32 | INFO     | __main__:main:238 |    按 'q' 或 ESC 退出程序
2025-08-04 17:03:32 | INFO     | __main__:main:239 |    按 'r' 重置状态机
2025-08-04 17:03:32 | INFO     | __main__:main:240 |    按 'd' 切换调试模式
2025-08-04 17:03:32 | INFO     | __main__:main:242 | 🔍 开始检测...
2025-08-04 17:03:32 | INFO     | __main__:main:243 | ------------------------------------------------------------
2025-08-04 17:03:32 | INFO     | src.state_machine.action_detector:run:139 | 开始运行动作检测
2025-08-04 17:03:34 | INFO     | src.detection.detector:detect:223 | 检测调试 #1: 原始检测数=2, 过滤后检测数=2, 置信度阈值=0.5
2025-08-04 17:03:34 | INFO     | src.detection.detector:detect:225 | 推理时间: 1098.3ms, 平均: 1098.3ms
2025-08-04 17:03:34 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 17:03:34 | INFO     | src.detection.detector:detect:233 | 原始类别: [2.0, 1.0]
2025-08-04 17:03:34 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.8734681606292725, 0.8511121273040771]
2025-08-04 17:03:34 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 1: 检测到 2 个对象
2025-08-04 17:03:34 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:03:34 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.851, 位置: [1146, 236, 1421, 522]
2025-08-04 17:03:34 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:03:34 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.873, 位置: [1102, 515, 1342, 707]
2025-08-04 17:03:34 | INFO     | src.detection.detector:detect:223 | 检测调试 #2: 原始检测数=2, 过滤后检测数=2, 置信度阈值=0.5
2025-08-04 17:03:34 | INFO     | src.detection.detector:detect:225 | 推理时间: 7.1ms, 平均: 552.7ms
2025-08-04 17:03:34 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 17:03:34 | INFO     | src.detection.detector:detect:233 | 原始类别: [1.0, 2.0]
2025-08-04 17:03:34 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.8752053380012512, 0.870661199092865]
2025-08-04 17:03:34 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 2: 检测到 2 个对象
2025-08-04 17:03:34 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:03:34 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.875, 位置: [1146, 237, 1420, 522]
2025-08-04 17:03:34 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:03:34 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.871, 位置: [1102, 514, 1342, 706]
2025-08-04 17:03:34 | INFO     | src.detection.detector:detect:223 | 检测调试 #3: 原始检测数=2, 过滤后检测数=2, 置信度阈值=0.5
2025-08-04 17:03:34 | INFO     | src.detection.detector:detect:225 | 推理时间: 6.7ms, 平均: 370.7ms
2025-08-04 17:03:34 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 17:03:34 | INFO     | src.detection.detector:detect:233 | 原始类别: [1.0, 2.0]
2025-08-04 17:03:34 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.8700747489929199, 0.861918032169342]
2025-08-04 17:03:34 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 3: 检测到 2 个对象
2025-08-04 17:03:34 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:03:34 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.870, 位置: [1145, 237, 1420, 522]
2025-08-04 17:03:34 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:03:34 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.862, 位置: [1102, 515, 1342, 707]
2025-08-04 17:03:34 | INFO     | src.detection.detector:detect:223 | 检测调试 #4: 原始检测数=2, 过滤后检测数=2, 置信度阈值=0.5
2025-08-04 17:03:34 | INFO     | src.detection.detector:detect:225 | 推理时间: 5.8ms, 平均: 279.5ms
2025-08-04 17:03:34 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 17:03:34 | INFO     | src.detection.detector:detect:233 | 原始类别: [1.0, 2.0]
2025-08-04 17:03:34 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.8732701539993286, 0.8566346168518066]
2025-08-04 17:03:34 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 4: 检测到 2 个对象
2025-08-04 17:03:34 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:03:34 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.873, 位置: [1145, 237, 1420, 522]
2025-08-04 17:03:34 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:03:34 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.857, 位置: [1102, 515, 1342, 707]
2025-08-04 17:03:34 | INFO     | src.detection.detector:detect:223 | 检测调试 #5: 原始检测数=2, 过滤后检测数=2, 置信度阈值=0.5
2025-08-04 17:03:34 | INFO     | src.detection.detector:detect:225 | 推理时间: 6.2ms, 平均: 224.8ms
2025-08-04 17:03:34 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 17:03:34 | INFO     | src.detection.detector:detect:233 | 原始类别: [2.0, 1.0]
2025-08-04 17:03:34 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.8743568062782288, 0.8706481456756592]
2025-08-04 17:03:34 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 5: 检测到 2 个对象
2025-08-04 17:03:34 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:03:34 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.871, 位置: [1145, 237, 1420, 522]
2025-08-04 17:03:34 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:03:34 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.874, 位置: [1102, 514, 1342, 706]
2025-08-04 17:03:34 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 6: 检测到 2 个对象
2025-08-04 17:03:34 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:03:34 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.867, 位置: [1145, 237, 1420, 522]
2025-08-04 17:03:34 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:03:34 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.872, 位置: [1102, 514, 1342, 706]
2025-08-04 17:03:34 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 7: 检测到 2 个对象
2025-08-04 17:03:34 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:03:34 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.864, 位置: [1145, 237, 1420, 522]
2025-08-04 17:03:34 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:03:34 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.871, 位置: [1102, 514, 1342, 706]
2025-08-04 17:03:34 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 8: 检测到 2 个对象
2025-08-04 17:03:34 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:03:34 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.862, 位置: [1145, 237, 1420, 522]
2025-08-04 17:03:34 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:03:34 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.868, 位置: [1102, 514, 1342, 706]
2025-08-04 17:03:34 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 9: 检测到 2 个对象
2025-08-04 17:03:34 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:03:34 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.869, 位置: [1145, 237, 1420, 522]
2025-08-04 17:03:34 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:03:34 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.864, 位置: [1102, 514, 1342, 706]
2025-08-04 17:03:34 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 10: 检测到 2 个对象
2025-08-04 17:03:34 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:03:34 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.874, 位置: [1146, 237, 1420, 522]
2025-08-04 17:03:34 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:03:34 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.868, 位置: [1102, 514, 1342, 706]
2025-08-04 17:03:37 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 100: 检测到 2 个对象
2025-08-04 17:03:37 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:03:37 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.900, 位置: [1145, 237, 1420, 522]
2025-08-04 17:03:37 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:03:37 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.873, 位置: [1102, 514, 1342, 706]
2025-08-04 17:03:40 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 200: 检测到 2 个对象
2025-08-04 17:03:40 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:03:40 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.886, 位置: [1147, 236, 1420, 522]
2025-08-04 17:03:40 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:03:40 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.860, 位置: [1102, 513, 1342, 707]
2025-08-04 17:03:44 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 300: 检测到 2 个对象
2025-08-04 17:03:44 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:03:44 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.902, 位置: [1147, 236, 1419, 522]
2025-08-04 17:03:44 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:03:44 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.845, 位置: [1102, 513, 1343, 707]
2025-08-04 17:03:44 | INFO     | src.state_machine.action_detector:_log_statistics:396 | 统计信息 - 帧数: 300, 运行时间: 11.2s, FPS: 26.9, 状态: idle, 动作数: 0
2025-08-04 17:03:44 | ERROR    | src.state_machine.action_detector:run:188 | 运行过程中出错: 'product_detections'
2025-08-04 17:03:44 | INFO     | src.detection.video_capture:release:203 | 视频捕获资源已释放
2025-08-04 17:03:44 | INFO     | src.state_machine.action_detector:_cleanup:433 | 检测完成 - 总帧数: 300, 总运行时间: 11.2s, 平均FPS: 26.8, 检测到的动作数: 0
2025-08-04 17:03:44 | INFO     | __main__:main:261 | ============================================================
2025-08-04 17:03:44 | INFO     | __main__:main:262 | 👋 程序结束
2025-08-04 17:03:44 | INFO     | __main__:main:263 | ============================================================
2025-08-04 17:07:45 | INFO     | src.utils.logger:setup_logger:55 | 日志系统初始化完成，级别: INFO
2025-08-04 17:07:45 | INFO     | src.utils.logger:setup_logger:57 | 日志文件: logs/action_detection.log
2025-08-04 17:07:45 | INFO     | __main__:main:170 | ============================================================
2025-08-04 17:07:45 | INFO     | __main__:main:171 | 🚀 YOLOv8实时动作检测系统启动
2025-08-04 17:07:45 | INFO     | __main__:main:172 | ============================================================
2025-08-04 17:07:45 | INFO     | __main__:main:178 | 使用命令行指定的视频文件: /home/<USER>/wolong_ws/yolo_detect_ws/data/test_videos/test_video_long.mp4
2025-08-04 17:07:45 | INFO     | __main__:main:189 | 📹 视频源: /home/<USER>/wolong_ws/yolo_detect_ws/data/test_videos/test_video_long.mp4
2025-08-04 17:07:45 | INFO     | __main__:main:190 | 🤖 模型路径: yolo_training/yolov8n_custom2/weights/best.pt
2025-08-04 17:07:45 | INFO     | __main__:main:191 | 📁 输出目录: output/detected_actions
2025-08-04 17:07:45 | INFO     | __main__:main:192 | 🎯 置信度阈值: 0.5
2025-08-04 17:07:45 | INFO     | __main__:main:193 | 📊 交互阈值 - IoU: 0.1, 距离: 50px
2025-08-04 17:07:45 | INFO     | __main__:main:194 | ⏰ 冷却时间: 3.0秒, 动作持续时间: 0.5秒
2025-08-04 17:07:45 | INFO     | src.detection.detector:_get_device:55 | torch.cuda.is_available(): True
2025-08-04 17:07:45 | INFO     | src.detection.detector:_get_device:56 | torch.cuda.device_count(): 1
2025-08-04 17:07:45 | INFO     | src.detection.detector:_get_device:62 | 检测到GPU: NVIDIA GeForce RTX 4060 Laptop GPU (7.6GB)
2025-08-04 17:07:45 | INFO     | src.detection.detector:_get_device:66 | 使用GPU设备: cuda:0
2025-08-04 17:07:45 | INFO     | src.detection.detector:_get_device:88 | 使用设备: 0
2025-08-04 17:07:45 | INFO     | src.detection.detector:_get_device:97 | GPU缓存已清理
2025-08-04 17:07:45 | INFO     | src.detection.detector:_get_device:101 | GPU内存分配策略已设置
2025-08-04 17:07:45 | INFO     | src.detection.detector:_load_model:117 | 加载模型: yolo_training/yolov8n_custom2/weights/best.pt
2025-08-04 17:07:45 | INFO     | src.detection.detector:_load_model:123 | 将模型移动到GPU设备: 0
2025-08-04 17:07:45 | INFO     | src.detection.detector:_load_model:129 | 模型已成功移动到GPU
2025-08-04 17:07:45 | INFO     | src.detection.detector:_load_model:138 | 检测类别: {0: 'blue_box', 1: 'upper_yellow_box', 2: 'lower_yellow_box', 3: 'hand'}
2025-08-04 17:07:45 | INFO     | src.detection.video_capture:_initialize_capture:52 | 初始化视频文件: /home/<USER>/wolong_ws/yolo_detect_ws/data/test_videos/test_video_long.mp4
2025-08-04 17:07:45 | INFO     | src.detection.video_capture:_log_video_info:74 | 视频信息 - 分辨率: 1442x898, FPS: 25.00
2025-08-04 17:07:45 | INFO     | src.detection.video_capture:_log_video_info:79 | 视频时长: 304.00秒, 总帧数: 7600
2025-08-04 17:07:45 | INFO     | src.state_machine.action_state_machine:__init__:73 | 状态机初始化完成，初始状态: idle
2025-08-04 17:07:45 | INFO     | src.utils.video_buffer:__init__:33 | 视频缓存器初始化完成，缓存大小: 31帧，帧率: 30.0fps
2025-08-04 17:07:45 | INFO     | src.utils.video_buffer:__init__:168 | 确保目录存在: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_ok
2025-08-04 17:07:45 | INFO     | src.utils.video_buffer:__init__:168 | 确保目录存在: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_nok_electric
2025-08-04 17:07:45 | INFO     | src.utils.video_buffer:__init__:168 | 确保目录存在: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_nok_appearence
2025-08-04 17:07:45 | INFO     | src.state_machine.action_detector:__init__:96 | 视频保存功能已启用，缓存大小: 31帧
2025-08-04 17:07:45 | INFO     | src.state_machine.action_detector:__init__:120 | 动作检测器初始化完成
2025-08-04 17:07:45 | INFO     | __main__:main:226 | 🖥️  推理设备: 0
2025-08-04 17:07:45 | INFO     | __main__:main:237 | 💡 控制提示:
2025-08-04 17:07:45 | INFO     | __main__:main:238 |    按 'q' 或 ESC 退出程序
2025-08-04 17:07:45 | INFO     | __main__:main:239 |    按 'r' 重置状态机
2025-08-04 17:07:45 | INFO     | __main__:main:240 |    按 'd' 切换调试模式
2025-08-04 17:07:45 | INFO     | __main__:main:242 | 🔍 开始检测...
2025-08-04 17:07:45 | INFO     | __main__:main:243 | ------------------------------------------------------------
2025-08-04 17:07:45 | INFO     | src.state_machine.action_detector:run:139 | 开始运行动作检测
2025-08-04 17:07:46 | INFO     | src.detection.detector:detect:223 | 检测调试 #1: 原始检测数=3, 过滤后检测数=3, 置信度阈值=0.5
2025-08-04 17:07:46 | INFO     | src.detection.detector:detect:225 | 推理时间: 1289.2ms, 平均: 1289.2ms
2025-08-04 17:07:46 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 17:07:46 | INFO     | src.detection.detector:detect:233 | 原始类别: [2.0, 0.0, 1.0]
2025-08-04 17:07:46 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.9483245611190796, 0.9287644624710083, 0.9003223180770874]
2025-08-04 17:07:46 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 1: 检测到 3 个对象
2025-08-04 17:07:46 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:07:46 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.929, 位置: [0, 586, 491, 898]
2025-08-04 17:07:46 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:07:46 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.900, 位置: [1168, 223, 1426, 518]
2025-08-04 17:07:46 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:07:46 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.948, 位置: [1105, 507, 1348, 711]
2025-08-04 17:07:46 | INFO     | src.detection.detector:detect:223 | 检测调试 #2: 原始检测数=3, 过滤后检测数=3, 置信度阈值=0.5
2025-08-04 17:07:46 | INFO     | src.detection.detector:detect:225 | 推理时间: 8.5ms, 平均: 648.9ms
2025-08-04 17:07:46 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 17:07:46 | INFO     | src.detection.detector:detect:233 | 原始类别: [2.0, 0.0, 1.0]
2025-08-04 17:07:46 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.9426160454750061, 0.92695152759552, 0.9040740132331848]
2025-08-04 17:07:46 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 2: 检测到 3 个对象
2025-08-04 17:07:46 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:07:46 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.927, 位置: [1, 596, 490, 898]
2025-08-04 17:07:46 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:07:46 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.904, 位置: [1169, 223, 1426, 518]
2025-08-04 17:07:46 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:07:46 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.943, 位置: [1104, 507, 1348, 711]
2025-08-04 17:07:46 | INFO     | src.detection.detector:detect:223 | 检测调试 #3: 原始检测数=3, 过滤后检测数=3, 置信度阈值=0.5
2025-08-04 17:07:46 | INFO     | src.detection.detector:detect:225 | 推理时间: 7.9ms, 平均: 435.2ms
2025-08-04 17:07:46 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 17:07:46 | INFO     | src.detection.detector:detect:233 | 原始类别: [2.0, 0.0, 1.0]
2025-08-04 17:07:46 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.9422558546066284, 0.9203870296478271, 0.9028117656707764]
2025-08-04 17:07:46 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 3: 检测到 3 个对象
2025-08-04 17:07:46 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:07:46 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.920, 位置: [1, 596, 490, 898]
2025-08-04 17:07:46 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:07:46 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.903, 位置: [1169, 223, 1426, 518]
2025-08-04 17:07:46 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:07:46 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.942, 位置: [1104, 507, 1348, 711]
2025-08-04 17:07:46 | INFO     | src.detection.detector:detect:223 | 检测调试 #4: 原始检测数=3, 过滤后检测数=3, 置信度阈值=0.5
2025-08-04 17:07:46 | INFO     | src.detection.detector:detect:225 | 推理时间: 6.6ms, 平均: 328.1ms
2025-08-04 17:07:46 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 17:07:46 | INFO     | src.detection.detector:detect:233 | 原始类别: [2.0, 0.0, 1.0]
2025-08-04 17:07:46 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.9385360479354858, 0.9256897568702698, 0.9024756550788879]
2025-08-04 17:07:46 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 4: 检测到 3 个对象
2025-08-04 17:07:46 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:07:46 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.926, 位置: [1, 596, 490, 898]
2025-08-04 17:07:46 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:07:46 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.902, 位置: [1169, 223, 1426, 518]
2025-08-04 17:07:46 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:07:46 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.939, 位置: [1105, 507, 1348, 711]
2025-08-04 17:07:46 | INFO     | src.detection.detector:detect:223 | 检测调试 #5: 原始检测数=3, 过滤后检测数=3, 置信度阈值=0.5
2025-08-04 17:07:46 | INFO     | src.detection.detector:detect:225 | 推理时间: 7.1ms, 平均: 263.9ms
2025-08-04 17:07:46 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 17:07:46 | INFO     | src.detection.detector:detect:233 | 原始类别: [2.0, 0.0, 1.0]
2025-08-04 17:07:46 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.9389451146125793, 0.9274168610572815, 0.9030306935310364]
2025-08-04 17:07:46 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 5: 检测到 3 个对象
2025-08-04 17:07:46 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:07:46 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.927, 位置: [1, 596, 490, 898]
2025-08-04 17:07:46 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:07:46 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.903, 位置: [1169, 223, 1426, 518]
2025-08-04 17:07:46 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:07:46 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.939, 位置: [1104, 507, 1348, 711]
2025-08-04 17:07:46 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 6: 检测到 3 个对象
2025-08-04 17:07:46 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:07:46 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.926, 位置: [1, 596, 490, 898]
2025-08-04 17:07:46 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:07:46 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.903, 位置: [1168, 223, 1426, 518]
2025-08-04 17:07:46 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:07:46 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.936, 位置: [1104, 507, 1348, 711]
2025-08-04 17:07:46 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 7: 检测到 3 个对象
2025-08-04 17:07:46 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:07:46 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.926, 位置: [1, 596, 490, 898]
2025-08-04 17:07:46 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:07:46 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.902, 位置: [1169, 223, 1426, 518]
2025-08-04 17:07:46 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:07:46 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.943, 位置: [1105, 507, 1348, 711]
2025-08-04 17:07:47 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 8: 检测到 3 个对象
2025-08-04 17:07:47 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:07:47 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.923, 位置: [1, 596, 490, 898]
2025-08-04 17:07:47 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:07:47 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.901, 位置: [1169, 223, 1426, 518]
2025-08-04 17:07:47 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:07:47 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.943, 位置: [1105, 507, 1348, 711]
2025-08-04 17:07:47 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 9: 检测到 3 个对象
2025-08-04 17:07:47 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:07:47 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.923, 位置: [1, 596, 489, 898]
2025-08-04 17:07:47 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:07:47 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.899, 位置: [1169, 223, 1426, 518]
2025-08-04 17:07:47 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:07:47 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.943, 位置: [1105, 507, 1348, 711]
2025-08-04 17:07:47 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 10: 检测到 3 个对象
2025-08-04 17:07:47 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:07:47 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.921, 位置: [1, 596, 489, 898]
2025-08-04 17:07:47 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:07:47 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.900, 位置: [1168, 223, 1426, 518]
2025-08-04 17:07:47 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:07:47 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.942, 位置: [1105, 507, 1348, 711]
2025-08-04 17:07:50 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 100: 检测到 4 个对象
2025-08-04 17:07:50 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - hand: 1 个
2025-08-04 17:07:50 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.574, 位置: [296, 601, 439, 718]
2025-08-04 17:07:50 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:07:50 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.865, 位置: [0, 572, 488, 898]
2025-08-04 17:07:50 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:07:50 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.906, 位置: [1168, 224, 1426, 518]
2025-08-04 17:07:50 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:07:50 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.941, 位置: [1104, 507, 1348, 711]
2025-08-04 17:07:50 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与blue_box交互，IoU: 0.105, 距离: 144.9
2025-08-04 17:07:50 | INFO     | src.state_machine.action_state_machine:_handle_detecting_state:130 | 手部与blue_box的交互中断
2025-08-04 17:07:53 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 200: 检测到 3 个对象
2025-08-04 17:07:53 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:07:53 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.930, 位置: [0, 586, 492, 898]
2025-08-04 17:07:53 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:07:53 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.900, 位置: [1168, 224, 1426, 518]
2025-08-04 17:07:53 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:07:53 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.944, 位置: [1106, 507, 1348, 711]
2025-08-04 17:07:56 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与blue_box交互，IoU: 0.135, 距离: 157.3
2025-08-04 17:07:56 | SUCCESS  | src.state_machine.action_state_machine:_handle_detecting_state:155 | 检测到ok_action！第1次动作，持续时间: 0.50秒
2025-08-04 17:07:56 | SUCCESS  | src.state_machine.action_detector:_handle_action_detected:198 | 检测到动作: action_1754298476_1 (类型: ok_action)
2025-08-04 17:07:56 | SUCCESS  | src.utils.video_buffer:save_action_video:117 | 动作视频已保存: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_ok/action_1754298476_1_20250804_170756.mp4 (31帧)
2025-08-04 17:07:56 | SUCCESS  | src.state_machine.action_detector:_handle_action_detected:206 | 动作视频已保存: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_ok/action_1754298476_1_20250804_170756.mp4
2025-08-04 17:07:56 | ERROR    | src.state_machine.action_detector:_save_action_image:246 | 保存动作图像失败: 'tracked_object'
2025-08-04 17:07:56 | SUCCESS  | __main__:action_callback:132 | 🎯 检测到动作 #action_1754298476_1
2025-08-04 17:07:56 | INFO     | __main__:action_callback:133 |    动作类型: ✅ OK动作 - 产品放入蓝色箱子
2025-08-04 17:07:56 | INFO     | __main__:action_callback:134 |    容器类型: blue_box
2025-08-04 17:07:56 | INFO     | __main__:action_callback:135 |    时间戳: 17:07:56
2025-08-04 17:07:56 | INFO     | __main__:action_callback:139 |    交互时长: 0.50秒
2025-08-04 17:07:56 | INFO     | __main__:action_callback:140 |    IoU: 0.128
2025-08-04 17:07:56 | INFO     | __main__:action_callback:141 |    距离: 18.9px
2025-08-04 17:07:56 | INFO     | __main__:action_callback:142 |    置信度: 0.644
2025-08-04 17:07:56 | INFO     | __main__:action_callback:145 |    保存路径: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_ok/
2025-08-04 17:07:56 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 300: 检测到 3 个对象
2025-08-04 17:07:56 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:07:56 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.946, 位置: [4, 616, 478, 898]
2025-08-04 17:07:56 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:07:56 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.903, 位置: [1169, 223, 1426, 518]
2025-08-04 17:07:56 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:07:56 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.933, 位置: [1103, 507, 1348, 710]
2025-08-04 17:07:56 | INFO     | src.state_machine.action_detector:_log_statistics:396 | 统计信息 - 帧数: 300, 运行时间: 11.5s, FPS: 26.0, 状态: idle, 动作数: 1
2025-08-04 17:07:56 | ERROR    | src.state_machine.action_detector:run:188 | 运行过程中出错: 'product_detections'
2025-08-04 17:07:56 | INFO     | src.detection.video_capture:release:203 | 视频捕获资源已释放
2025-08-04 17:07:56 | INFO     | src.state_machine.action_detector:_cleanup:433 | 检测完成 - 总帧数: 300, 总运行时间: 11.6s, 平均FPS: 26.0, 检测到的动作数: 1
2025-08-04 17:07:56 | INFO     | __main__:main:261 | ============================================================
2025-08-04 17:07:56 | INFO     | __main__:main:262 | 👋 程序结束
2025-08-04 17:07:56 | INFO     | __main__:main:263 | ============================================================
2025-08-04 17:09:29 | INFO     | src.utils.logger:setup_logger:55 | 日志系统初始化完成，级别: INFO
2025-08-04 17:09:29 | INFO     | src.utils.logger:setup_logger:57 | 日志文件: logs/action_detection.log
2025-08-04 17:09:29 | INFO     | __main__:main:170 | ============================================================
2025-08-04 17:09:29 | INFO     | __main__:main:171 | 🚀 YOLOv8实时动作检测系统启动
2025-08-04 17:09:29 | INFO     | __main__:main:172 | ============================================================
2025-08-04 17:09:29 | INFO     | __main__:main:178 | 使用命令行指定的视频文件: /home/<USER>/wolong_ws/yolo_detect_ws/data/test_videos/test_video_nok_app.mp4
2025-08-04 17:09:29 | INFO     | __main__:main:189 | 📹 视频源: /home/<USER>/wolong_ws/yolo_detect_ws/data/test_videos/test_video_nok_app.mp4
2025-08-04 17:09:29 | INFO     | __main__:main:190 | 🤖 模型路径: yolo_training/yolov8n_custom2/weights/best.pt
2025-08-04 17:09:29 | INFO     | __main__:main:191 | 📁 输出目录: output/detected_actions
2025-08-04 17:09:29 | INFO     | __main__:main:192 | 🎯 置信度阈值: 0.5
2025-08-04 17:09:29 | INFO     | __main__:main:193 | 📊 交互阈值 - IoU: 0.1, 距离: 50px
2025-08-04 17:09:29 | INFO     | __main__:main:194 | ⏰ 冷却时间: 3.0秒, 动作持续时间: 0.5秒
2025-08-04 17:09:29 | INFO     | src.detection.detector:_get_device:55 | torch.cuda.is_available(): True
2025-08-04 17:09:29 | INFO     | src.detection.detector:_get_device:56 | torch.cuda.device_count(): 1
2025-08-04 17:09:29 | INFO     | src.detection.detector:_get_device:62 | 检测到GPU: NVIDIA GeForce RTX 4060 Laptop GPU (7.6GB)
2025-08-04 17:09:29 | INFO     | src.detection.detector:_get_device:66 | 使用GPU设备: cuda:0
2025-08-04 17:09:29 | INFO     | src.detection.detector:_get_device:88 | 使用设备: 0
2025-08-04 17:09:29 | INFO     | src.detection.detector:_get_device:97 | GPU缓存已清理
2025-08-04 17:09:29 | INFO     | src.detection.detector:_get_device:101 | GPU内存分配策略已设置
2025-08-04 17:09:29 | INFO     | src.detection.detector:_load_model:117 | 加载模型: yolo_training/yolov8n_custom2/weights/best.pt
2025-08-04 17:09:29 | INFO     | src.detection.detector:_load_model:123 | 将模型移动到GPU设备: 0
2025-08-04 17:09:29 | INFO     | src.detection.detector:_load_model:129 | 模型已成功移动到GPU
2025-08-04 17:09:29 | INFO     | src.detection.detector:_load_model:138 | 检测类别: {0: 'blue_box', 1: 'upper_yellow_box', 2: 'lower_yellow_box', 3: 'hand'}
2025-08-04 17:09:29 | INFO     | src.detection.video_capture:_initialize_capture:52 | 初始化视频文件: /home/<USER>/wolong_ws/yolo_detect_ws/data/test_videos/test_video_nok_app.mp4
2025-08-04 17:09:29 | INFO     | src.detection.video_capture:_log_video_info:74 | 视频信息 - 分辨率: 1442x898, FPS: 25.00
2025-08-04 17:09:29 | INFO     | src.detection.video_capture:_log_video_info:79 | 视频时长: 2.20秒, 总帧数: 55
2025-08-04 17:09:29 | INFO     | src.state_machine.action_state_machine:__init__:73 | 状态机初始化完成，初始状态: idle
2025-08-04 17:09:29 | INFO     | src.utils.video_buffer:__init__:33 | 视频缓存器初始化完成，缓存大小: 31帧，帧率: 30.0fps
2025-08-04 17:09:29 | INFO     | src.utils.video_buffer:__init__:168 | 确保目录存在: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_ok
2025-08-04 17:09:29 | INFO     | src.utils.video_buffer:__init__:168 | 确保目录存在: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_nok_electric
2025-08-04 17:09:29 | INFO     | src.utils.video_buffer:__init__:168 | 确保目录存在: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_nok_appearence
2025-08-04 17:09:29 | INFO     | src.state_machine.action_detector:__init__:96 | 视频保存功能已启用，缓存大小: 31帧
2025-08-04 17:09:29 | INFO     | src.state_machine.action_detector:__init__:120 | 动作检测器初始化完成
2025-08-04 17:09:29 | INFO     | __main__:main:226 | 🖥️  推理设备: 0
2025-08-04 17:09:29 | INFO     | __main__:main:237 | 💡 控制提示:
2025-08-04 17:09:29 | INFO     | __main__:main:238 |    按 'q' 或 ESC 退出程序
2025-08-04 17:09:29 | INFO     | __main__:main:239 |    按 'r' 重置状态机
2025-08-04 17:09:29 | INFO     | __main__:main:240 |    按 'd' 切换调试模式
2025-08-04 17:09:29 | INFO     | __main__:main:242 | 🔍 开始检测...
2025-08-04 17:09:29 | INFO     | __main__:main:243 | ------------------------------------------------------------
2025-08-04 17:09:29 | INFO     | src.state_machine.action_detector:run:139 | 开始运行动作检测
2025-08-04 17:09:30 | INFO     | src.detection.detector:detect:223 | 检测调试 #1: 原始检测数=3, 过滤后检测数=3, 置信度阈值=0.5
2025-08-04 17:09:30 | INFO     | src.detection.detector:detect:225 | 推理时间: 1267.6ms, 平均: 1267.6ms
2025-08-04 17:09:30 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 17:09:30 | INFO     | src.detection.detector:detect:233 | 原始类别: [1.0, 0.0, 2.0]
2025-08-04 17:09:30 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.7444828152656555, 0.6237179040908813, 0.6118455529212952]
2025-08-04 17:09:30 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 1: 检测到 3 个对象
2025-08-04 17:09:30 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:09:30 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.624, 位置: [0, 482, 332, 898]
2025-08-04 17:09:30 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:09:30 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.744, 位置: [1142, 235, 1423, 525]
2025-08-04 17:09:30 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:09:30 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.612, 位置: [1099, 509, 1341, 710]
2025-08-04 17:09:30 | INFO     | src.detection.detector:detect:223 | 检测调试 #2: 原始检测数=3, 过滤后检测数=3, 置信度阈值=0.5
2025-08-04 17:09:30 | INFO     | src.detection.detector:detect:225 | 推理时间: 8.4ms, 平均: 638.0ms
2025-08-04 17:09:30 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 17:09:30 | INFO     | src.detection.detector:detect:233 | 原始类别: [1.0, 2.0, 0.0]
2025-08-04 17:09:30 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.760984480381012, 0.6219632029533386, 0.620657742023468]
2025-08-04 17:09:30 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 2: 检测到 3 个对象
2025-08-04 17:09:30 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:09:30 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.621, 位置: [1, 482, 333, 898]
2025-08-04 17:09:30 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:09:30 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.761, 位置: [1143, 235, 1423, 525]
2025-08-04 17:09:30 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:09:30 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.622, 位置: [1099, 509, 1341, 710]
2025-08-04 17:09:30 | INFO     | src.detection.detector:detect:223 | 检测调试 #3: 原始检测数=3, 过滤后检测数=3, 置信度阈值=0.5
2025-08-04 17:09:30 | INFO     | src.detection.detector:detect:225 | 推理时间: 7.2ms, 平均: 427.7ms
2025-08-04 17:09:30 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 17:09:30 | INFO     | src.detection.detector:detect:233 | 原始类别: [1.0, 2.0, 0.0]
2025-08-04 17:09:30 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.7685647010803223, 0.6255905628204346, 0.6197748780250549]
2025-08-04 17:09:30 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 3: 检测到 3 个对象
2025-08-04 17:09:30 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:09:30 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.620, 位置: [1, 482, 331, 898]
2025-08-04 17:09:30 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:09:30 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.769, 位置: [1142, 235, 1423, 525]
2025-08-04 17:09:30 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:09:30 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.626, 位置: [1099, 509, 1341, 710]
2025-08-04 17:09:30 | INFO     | src.detection.detector:detect:223 | 检测调试 #4: 原始检测数=3, 过滤后检测数=3, 置信度阈值=0.5
2025-08-04 17:09:30 | INFO     | src.detection.detector:detect:225 | 推理时间: 7.3ms, 平均: 322.6ms
2025-08-04 17:09:30 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 17:09:30 | INFO     | src.detection.detector:detect:233 | 原始类别: [1.0, 0.0, 2.0]
2025-08-04 17:09:30 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.7928094863891602, 0.6179611086845398, 0.5988032817840576]
2025-08-04 17:09:30 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 4: 检测到 3 个对象
2025-08-04 17:09:30 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:09:30 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.618, 位置: [0, 482, 330, 898]
2025-08-04 17:09:30 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:09:30 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.793, 位置: [1142, 234, 1423, 525]
2025-08-04 17:09:30 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:09:30 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.599, 位置: [1099, 509, 1341, 710]
2025-08-04 17:09:30 | INFO     | src.detection.detector:detect:223 | 检测调试 #5: 原始检测数=3, 过滤后检测数=3, 置信度阈值=0.5
2025-08-04 17:09:30 | INFO     | src.detection.detector:detect:225 | 推理时间: 8.2ms, 平均: 259.7ms
2025-08-04 17:09:30 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 17:09:30 | INFO     | src.detection.detector:detect:233 | 原始类别: [1.0, 0.0, 2.0]
2025-08-04 17:09:30 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.7898095846176147, 0.6177279353141785, 0.6096243858337402]
2025-08-04 17:09:30 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 5: 检测到 3 个对象
2025-08-04 17:09:30 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:09:30 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.618, 位置: [0, 482, 329, 898]
2025-08-04 17:09:30 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:09:30 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.790, 位置: [1142, 235, 1423, 525]
2025-08-04 17:09:30 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:09:30 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.610, 位置: [1099, 510, 1341, 710]
2025-08-04 17:09:31 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 6: 检测到 3 个对象
2025-08-04 17:09:31 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:09:31 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.614, 位置: [0, 482, 330, 898]
2025-08-04 17:09:31 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:09:31 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.795, 位置: [1142, 235, 1423, 525]
2025-08-04 17:09:31 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:09:31 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.605, 位置: [1099, 509, 1341, 710]
2025-08-04 17:09:31 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 7: 检测到 4 个对象
2025-08-04 17:09:31 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - hand: 1 个
2025-08-04 17:09:31 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.597, 位置: [824, 524, 988, 616]
2025-08-04 17:09:31 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:09:31 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.609, 位置: [0, 482, 328, 898]
2025-08-04 17:09:31 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:09:31 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.786, 位置: [1142, 235, 1422, 525]
2025-08-04 17:09:31 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:09:31 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.526, 位置: [1099, 510, 1341, 710]
2025-08-04 17:09:31 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 8: 检测到 2 个对象
2025-08-04 17:09:31 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:09:31 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.609, 位置: [1, 482, 331, 898]
2025-08-04 17:09:31 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:09:31 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.769, 位置: [1142, 235, 1422, 525]
2025-08-04 17:09:31 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 9: 检测到 3 个对象
2025-08-04 17:09:31 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - hand: 1 个
2025-08-04 17:09:31 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.626, 位置: [861, 548, 1014, 628]
2025-08-04 17:09:31 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:09:31 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.612, 位置: [1, 482, 331, 898]
2025-08-04 17:09:31 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:09:31 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.769, 位置: [1141, 234, 1422, 525]
2025-08-04 17:09:31 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 10: 检测到 2 个对象
2025-08-04 17:09:31 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:09:31 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.613, 位置: [1, 482, 333, 898]
2025-08-04 17:09:31 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:09:31 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.768, 位置: [1142, 234, 1422, 525]
2025-08-04 17:09:32 | INFO     | src.detection.video_capture:get_frame_generator:127 | 视频文件读取完毕
2025-08-04 17:09:32 | INFO     | src.detection.video_capture:release:203 | 视频捕获资源已释放
2025-08-04 17:09:32 | INFO     | src.state_machine.action_detector:_cleanup:433 | 检测完成 - 总帧数: 55, 总运行时间: 3.2s, 平均FPS: 17.4, 检测到的动作数: 0
2025-08-04 17:09:32 | INFO     | __main__:main:261 | ============================================================
2025-08-04 17:09:32 | INFO     | __main__:main:262 | 👋 程序结束
2025-08-04 17:09:32 | INFO     | __main__:main:263 | ============================================================
2025-08-04 17:09:38 | INFO     | src.utils.logger:setup_logger:55 | 日志系统初始化完成，级别: INFO
2025-08-04 17:09:38 | INFO     | src.utils.logger:setup_logger:57 | 日志文件: logs/action_detection.log
2025-08-04 17:09:38 | INFO     | __main__:main:170 | ============================================================
2025-08-04 17:09:38 | INFO     | __main__:main:171 | 🚀 YOLOv8实时动作检测系统启动
2025-08-04 17:09:38 | INFO     | __main__:main:172 | ============================================================
2025-08-04 17:09:38 | INFO     | __main__:main:178 | 使用命令行指定的视频文件: /home/<USER>/wolong_ws/yolo_detect_ws/data/test_videos/test_video_nok_app.mp4
2025-08-04 17:09:38 | INFO     | __main__:main:189 | 📹 视频源: /home/<USER>/wolong_ws/yolo_detect_ws/data/test_videos/test_video_nok_app.mp4
2025-08-04 17:09:38 | INFO     | __main__:main:190 | 🤖 模型路径: yolo_training/yolov8n_custom2/weights/best.pt
2025-08-04 17:09:38 | INFO     | __main__:main:191 | 📁 输出目录: output/detected_actions
2025-08-04 17:09:38 | INFO     | __main__:main:192 | 🎯 置信度阈值: 0.5
2025-08-04 17:09:38 | INFO     | __main__:main:193 | 📊 交互阈值 - IoU: 0.1, 距离: 50px
2025-08-04 17:09:38 | INFO     | __main__:main:194 | ⏰ 冷却时间: 3.0秒, 动作持续时间: 0.5秒
2025-08-04 17:09:38 | INFO     | src.detection.detector:_get_device:55 | torch.cuda.is_available(): True
2025-08-04 17:09:38 | INFO     | src.detection.detector:_get_device:56 | torch.cuda.device_count(): 1
2025-08-04 17:09:38 | INFO     | src.detection.detector:_get_device:62 | 检测到GPU: NVIDIA GeForce RTX 4060 Laptop GPU (7.6GB)
2025-08-04 17:09:38 | INFO     | src.detection.detector:_get_device:66 | 使用GPU设备: cuda:0
2025-08-04 17:09:38 | INFO     | src.detection.detector:_get_device:88 | 使用设备: 0
2025-08-04 17:09:38 | INFO     | src.detection.detector:_get_device:97 | GPU缓存已清理
2025-08-04 17:09:39 | INFO     | src.detection.detector:_get_device:101 | GPU内存分配策略已设置
2025-08-04 17:09:39 | INFO     | src.detection.detector:_load_model:117 | 加载模型: yolo_training/yolov8n_custom2/weights/best.pt
2025-08-04 17:09:39 | INFO     | src.detection.detector:_load_model:123 | 将模型移动到GPU设备: 0
2025-08-04 17:09:39 | INFO     | src.detection.detector:_load_model:129 | 模型已成功移动到GPU
2025-08-04 17:09:39 | INFO     | src.detection.detector:_load_model:138 | 检测类别: {0: 'blue_box', 1: 'upper_yellow_box', 2: 'lower_yellow_box', 3: 'hand'}
2025-08-04 17:09:39 | INFO     | src.detection.video_capture:_initialize_capture:52 | 初始化视频文件: /home/<USER>/wolong_ws/yolo_detect_ws/data/test_videos/test_video_nok_app.mp4
2025-08-04 17:09:39 | INFO     | src.detection.video_capture:_log_video_info:74 | 视频信息 - 分辨率: 1442x898, FPS: 25.00
2025-08-04 17:09:39 | INFO     | src.detection.video_capture:_log_video_info:79 | 视频时长: 2.20秒, 总帧数: 55
2025-08-04 17:09:39 | INFO     | src.state_machine.action_state_machine:__init__:73 | 状态机初始化完成，初始状态: idle
2025-08-04 17:09:39 | INFO     | src.utils.video_buffer:__init__:33 | 视频缓存器初始化完成，缓存大小: 31帧，帧率: 30.0fps
2025-08-04 17:09:39 | INFO     | src.utils.video_buffer:__init__:168 | 确保目录存在: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_ok
2025-08-04 17:09:39 | INFO     | src.utils.video_buffer:__init__:168 | 确保目录存在: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_nok_electric
2025-08-04 17:09:39 | INFO     | src.utils.video_buffer:__init__:168 | 确保目录存在: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_nok_appearence
2025-08-04 17:09:39 | INFO     | src.state_machine.action_detector:__init__:96 | 视频保存功能已启用，缓存大小: 31帧
2025-08-04 17:09:39 | INFO     | src.state_machine.action_detector:__init__:120 | 动作检测器初始化完成
2025-08-04 17:09:39 | INFO     | __main__:main:226 | 🖥️  推理设备: 0
2025-08-04 17:09:39 | INFO     | __main__:main:237 | 💡 控制提示:
2025-08-04 17:09:39 | INFO     | __main__:main:238 |    按 'q' 或 ESC 退出程序
2025-08-04 17:09:39 | INFO     | __main__:main:239 |    按 'r' 重置状态机
2025-08-04 17:09:39 | INFO     | __main__:main:240 |    按 'd' 切换调试模式
2025-08-04 17:09:39 | INFO     | __main__:main:242 | 🔍 开始检测...
2025-08-04 17:09:39 | INFO     | __main__:main:243 | ------------------------------------------------------------
2025-08-04 17:09:39 | INFO     | src.state_machine.action_detector:run:139 | 开始运行动作检测
2025-08-04 17:09:40 | INFO     | src.detection.detector:detect:223 | 检测调试 #1: 原始检测数=3, 过滤后检测数=3, 置信度阈值=0.5
2025-08-04 17:09:40 | INFO     | src.detection.detector:detect:225 | 推理时间: 1264.4ms, 平均: 1264.4ms
2025-08-04 17:09:40 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 17:09:40 | INFO     | src.detection.detector:detect:233 | 原始类别: [1.0, 0.0, 2.0]
2025-08-04 17:09:40 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.7444828152656555, 0.6237179040908813, 0.6118455529212952]
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 1: 检测到 3 个对象
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.624, 位置: [0, 482, 332, 898]
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.744, 位置: [1142, 235, 1423, 525]
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.612, 位置: [1099, 509, 1341, 710]
2025-08-04 17:09:40 | INFO     | src.detection.detector:detect:223 | 检测调试 #2: 原始检测数=3, 过滤后检测数=3, 置信度阈值=0.5
2025-08-04 17:09:40 | INFO     | src.detection.detector:detect:225 | 推理时间: 8.3ms, 平均: 636.3ms
2025-08-04 17:09:40 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 17:09:40 | INFO     | src.detection.detector:detect:233 | 原始类别: [1.0, 2.0, 0.0]
2025-08-04 17:09:40 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.760984480381012, 0.6219632029533386, 0.620657742023468]
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 2: 检测到 3 个对象
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.621, 位置: [1, 482, 333, 898]
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.761, 位置: [1143, 235, 1423, 525]
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.622, 位置: [1099, 509, 1341, 710]
2025-08-04 17:09:40 | INFO     | src.detection.detector:detect:223 | 检测调试 #3: 原始检测数=3, 过滤后检测数=3, 置信度阈值=0.5
2025-08-04 17:09:40 | INFO     | src.detection.detector:detect:225 | 推理时间: 8.3ms, 平均: 427.0ms
2025-08-04 17:09:40 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 17:09:40 | INFO     | src.detection.detector:detect:233 | 原始类别: [1.0, 2.0, 0.0]
2025-08-04 17:09:40 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.7685647010803223, 0.6255905628204346, 0.6197748780250549]
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 3: 检测到 3 个对象
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.620, 位置: [1, 482, 331, 898]
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.769, 位置: [1142, 235, 1423, 525]
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.626, 位置: [1099, 509, 1341, 710]
2025-08-04 17:09:40 | INFO     | src.detection.detector:detect:223 | 检测调试 #4: 原始检测数=3, 过滤后检测数=3, 置信度阈值=0.5
2025-08-04 17:09:40 | INFO     | src.detection.detector:detect:225 | 推理时间: 9.0ms, 平均: 322.5ms
2025-08-04 17:09:40 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 17:09:40 | INFO     | src.detection.detector:detect:233 | 原始类别: [1.0, 0.0, 2.0]
2025-08-04 17:09:40 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.7928094863891602, 0.6179611086845398, 0.5988032817840576]
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 4: 检测到 3 个对象
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.618, 位置: [0, 482, 330, 898]
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.793, 位置: [1142, 234, 1423, 525]
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.599, 位置: [1099, 509, 1341, 710]
2025-08-04 17:09:40 | INFO     | src.detection.detector:detect:223 | 检测调试 #5: 原始检测数=3, 过滤后检测数=3, 置信度阈值=0.5
2025-08-04 17:09:40 | INFO     | src.detection.detector:detect:225 | 推理时间: 7.1ms, 平均: 259.4ms
2025-08-04 17:09:40 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 17:09:40 | INFO     | src.detection.detector:detect:233 | 原始类别: [1.0, 0.0, 2.0]
2025-08-04 17:09:40 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.7898095846176147, 0.6177279353141785, 0.6096243858337402]
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 5: 检测到 3 个对象
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.618, 位置: [0, 482, 329, 898]
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.790, 位置: [1142, 235, 1423, 525]
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.610, 位置: [1099, 510, 1341, 710]
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 6: 检测到 3 个对象
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.614, 位置: [0, 482, 330, 898]
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.795, 位置: [1142, 235, 1423, 525]
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.605, 位置: [1099, 509, 1341, 710]
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 7: 检测到 4 个对象
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - hand: 1 个
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.597, 位置: [824, 524, 988, 616]
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.609, 位置: [0, 482, 328, 898]
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.786, 位置: [1142, 235, 1422, 525]
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.526, 位置: [1099, 510, 1341, 710]
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 8: 检测到 2 个对象
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.609, 位置: [1, 482, 331, 898]
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.769, 位置: [1142, 235, 1422, 525]
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 9: 检测到 3 个对象
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - hand: 1 个
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.626, 位置: [861, 548, 1014, 628]
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.612, 位置: [1, 482, 331, 898]
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.769, 位置: [1141, 234, 1422, 525]
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 10: 检测到 2 个对象
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.613, 位置: [1, 482, 333, 898]
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:09:40 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.768, 位置: [1142, 234, 1422, 525]
2025-08-04 17:09:42 | INFO     | src.detection.video_capture:get_frame_generator:127 | 视频文件读取完毕
2025-08-04 17:09:42 | INFO     | src.detection.video_capture:release:203 | 视频捕获资源已释放
2025-08-04 17:09:42 | INFO     | src.state_machine.action_detector:_cleanup:434 | 检测完成 - 总帧数: 55, 总运行时间: 3.2s, 平均FPS: 17.4, 检测到的动作数: 0
2025-08-04 17:09:42 | INFO     | __main__:main:261 | ============================================================
2025-08-04 17:09:42 | INFO     | __main__:main:262 | 👋 程序结束
2025-08-04 17:09:42 | INFO     | __main__:main:263 | ============================================================
2025-08-04 17:09:53 | INFO     | src.utils.logger:setup_logger:55 | 日志系统初始化完成，级别: INFO
2025-08-04 17:09:53 | INFO     | src.utils.logger:setup_logger:57 | 日志文件: logs/action_detection.log
2025-08-04 17:09:53 | INFO     | __main__:main:170 | ============================================================
2025-08-04 17:09:53 | INFO     | __main__:main:171 | 🚀 YOLOv8实时动作检测系统启动
2025-08-04 17:09:53 | INFO     | __main__:main:172 | ============================================================
2025-08-04 17:09:53 | INFO     | __main__:main:178 | 使用命令行指定的视频文件: /home/<USER>/wolong_ws/yolo_detect_ws/data/test_videos/test_video_nok_ele.mp4
2025-08-04 17:09:53 | INFO     | __main__:main:189 | 📹 视频源: /home/<USER>/wolong_ws/yolo_detect_ws/data/test_videos/test_video_nok_ele.mp4
2025-08-04 17:09:53 | INFO     | __main__:main:190 | 🤖 模型路径: yolo_training/yolov8n_custom2/weights/best.pt
2025-08-04 17:09:53 | INFO     | __main__:main:191 | 📁 输出目录: output/detected_actions
2025-08-04 17:09:53 | INFO     | __main__:main:192 | 🎯 置信度阈值: 0.5
2025-08-04 17:09:53 | INFO     | __main__:main:193 | 📊 交互阈值 - IoU: 0.1, 距离: 50px
2025-08-04 17:09:53 | INFO     | __main__:main:194 | ⏰ 冷却时间: 3.0秒, 动作持续时间: 0.5秒
2025-08-04 17:09:53 | INFO     | src.detection.detector:_get_device:55 | torch.cuda.is_available(): True
2025-08-04 17:09:53 | INFO     | src.detection.detector:_get_device:56 | torch.cuda.device_count(): 1
2025-08-04 17:09:53 | INFO     | src.detection.detector:_get_device:62 | 检测到GPU: NVIDIA GeForce RTX 4060 Laptop GPU (7.6GB)
2025-08-04 17:09:53 | INFO     | src.detection.detector:_get_device:66 | 使用GPU设备: cuda:0
2025-08-04 17:09:53 | INFO     | src.detection.detector:_get_device:88 | 使用设备: 0
2025-08-04 17:09:53 | INFO     | src.detection.detector:_get_device:97 | GPU缓存已清理
2025-08-04 17:09:53 | INFO     | src.detection.detector:_get_device:101 | GPU内存分配策略已设置
2025-08-04 17:09:53 | INFO     | src.detection.detector:_load_model:117 | 加载模型: yolo_training/yolov8n_custom2/weights/best.pt
2025-08-04 17:09:53 | INFO     | src.detection.detector:_load_model:123 | 将模型移动到GPU设备: 0
2025-08-04 17:09:53 | INFO     | src.detection.detector:_load_model:129 | 模型已成功移动到GPU
2025-08-04 17:09:53 | INFO     | src.detection.detector:_load_model:138 | 检测类别: {0: 'blue_box', 1: 'upper_yellow_box', 2: 'lower_yellow_box', 3: 'hand'}
2025-08-04 17:09:53 | INFO     | src.detection.video_capture:_initialize_capture:52 | 初始化视频文件: /home/<USER>/wolong_ws/yolo_detect_ws/data/test_videos/test_video_nok_ele.mp4
2025-08-04 17:09:53 | INFO     | src.detection.video_capture:_log_video_info:74 | 视频信息 - 分辨率: 1442x898, FPS: 25.00
2025-08-04 17:09:53 | INFO     | src.detection.video_capture:_log_video_info:79 | 视频时长: 1.68秒, 总帧数: 42
2025-08-04 17:09:53 | INFO     | src.state_machine.action_state_machine:__init__:73 | 状态机初始化完成，初始状态: idle
2025-08-04 17:09:53 | INFO     | src.utils.video_buffer:__init__:33 | 视频缓存器初始化完成，缓存大小: 31帧，帧率: 30.0fps
2025-08-04 17:09:53 | INFO     | src.utils.video_buffer:__init__:168 | 确保目录存在: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_ok
2025-08-04 17:09:53 | INFO     | src.utils.video_buffer:__init__:168 | 确保目录存在: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_nok_electric
2025-08-04 17:09:53 | INFO     | src.utils.video_buffer:__init__:168 | 确保目录存在: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_nok_appearence
2025-08-04 17:09:53 | INFO     | src.state_machine.action_detector:__init__:96 | 视频保存功能已启用，缓存大小: 31帧
2025-08-04 17:09:53 | INFO     | src.state_machine.action_detector:__init__:120 | 动作检测器初始化完成
2025-08-04 17:09:53 | INFO     | __main__:main:226 | 🖥️  推理设备: 0
2025-08-04 17:09:53 | INFO     | __main__:main:237 | 💡 控制提示:
2025-08-04 17:09:53 | INFO     | __main__:main:238 |    按 'q' 或 ESC 退出程序
2025-08-04 17:09:53 | INFO     | __main__:main:239 |    按 'r' 重置状态机
2025-08-04 17:09:53 | INFO     | __main__:main:240 |    按 'd' 切换调试模式
2025-08-04 17:09:53 | INFO     | __main__:main:242 | 🔍 开始检测...
2025-08-04 17:09:53 | INFO     | __main__:main:243 | ------------------------------------------------------------
2025-08-04 17:09:53 | INFO     | src.state_machine.action_detector:run:139 | 开始运行动作检测
2025-08-04 17:09:54 | INFO     | src.detection.detector:detect:223 | 检测调试 #1: 原始检测数=2, 过滤后检测数=2, 置信度阈值=0.5
2025-08-04 17:09:54 | INFO     | src.detection.detector:detect:225 | 推理时间: 1282.2ms, 平均: 1282.2ms
2025-08-04 17:09:54 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 17:09:54 | INFO     | src.detection.detector:detect:233 | 原始类别: [1.0, 2.0]
2025-08-04 17:09:54 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.8922445178031921, 0.6277858018875122]
2025-08-04 17:09:54 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 1: 检测到 2 个对象
2025-08-04 17:09:54 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:09:54 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.892, 位置: [1142, 235, 1425, 524]
2025-08-04 17:09:54 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:09:54 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.628, 位置: [1099, 509, 1342, 710]
2025-08-04 17:09:54 | INFO     | src.detection.detector:detect:223 | 检测调试 #2: 原始检测数=2, 过滤后检测数=2, 置信度阈值=0.5
2025-08-04 17:09:54 | INFO     | src.detection.detector:detect:225 | 推理时间: 10.5ms, 平均: 646.4ms
2025-08-04 17:09:54 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 17:09:54 | INFO     | src.detection.detector:detect:233 | 原始类别: [1.0, 2.0]
2025-08-04 17:09:54 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.8924195170402527, 0.5876269340515137]
2025-08-04 17:09:54 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 2: 检测到 2 个对象
2025-08-04 17:09:54 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:09:54 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.892, 位置: [1142, 235, 1425, 524]
2025-08-04 17:09:54 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:09:54 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.588, 位置: [1099, 509, 1342, 710]
2025-08-04 17:09:54 | INFO     | src.detection.detector:detect:223 | 检测调试 #3: 原始检测数=2, 过滤后检测数=2, 置信度阈值=0.5
2025-08-04 17:09:54 | INFO     | src.detection.detector:detect:225 | 推理时间: 7.3ms, 平均: 433.3ms
2025-08-04 17:09:54 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 17:09:54 | INFO     | src.detection.detector:detect:233 | 原始类别: [1.0, 2.0]
2025-08-04 17:09:54 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.8966541886329651, 0.5911211967468262]
2025-08-04 17:09:54 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 3: 检测到 2 个对象
2025-08-04 17:09:54 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:09:54 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.897, 位置: [1142, 235, 1425, 524]
2025-08-04 17:09:54 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:09:54 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.591, 位置: [1099, 509, 1342, 710]
2025-08-04 17:09:54 | INFO     | src.detection.detector:detect:223 | 检测调试 #4: 原始检测数=2, 过滤后检测数=2, 置信度阈值=0.5
2025-08-04 17:09:54 | INFO     | src.detection.detector:detect:225 | 推理时间: 6.5ms, 平均: 326.6ms
2025-08-04 17:09:54 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 17:09:54 | INFO     | src.detection.detector:detect:233 | 原始类别: [1.0, 2.0]
2025-08-04 17:09:54 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.9046955704689026, 0.5451671481132507]
2025-08-04 17:09:54 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 4: 检测到 2 个对象
2025-08-04 17:09:54 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:09:54 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.905, 位置: [1141, 235, 1425, 524]
2025-08-04 17:09:54 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:09:54 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.545, 位置: [1099, 509, 1342, 710]
2025-08-04 17:09:55 | INFO     | src.detection.detector:detect:223 | 检测调试 #5: 原始检测数=1, 过滤后检测数=1, 置信度阈值=0.5
2025-08-04 17:09:55 | INFO     | src.detection.detector:detect:225 | 推理时间: 8.4ms, 平均: 263.0ms
2025-08-04 17:09:55 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 17:09:55 | INFO     | src.detection.detector:detect:233 | 原始类别: [1.0]
2025-08-04 17:09:55 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.9025538563728333]
2025-08-04 17:09:55 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 5: 检测到 1 个对象
2025-08-04 17:09:55 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:09:55 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.903, 位置: [1142, 234, 1424, 524]
2025-08-04 17:09:55 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 6: 检测到 2 个对象
2025-08-04 17:09:55 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:09:55 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.916, 位置: [1141, 234, 1424, 525]
2025-08-04 17:09:55 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:09:55 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.541, 位置: [1099, 510, 1341, 710]
2025-08-04 17:09:55 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 7: 检测到 2 个对象
2025-08-04 17:09:55 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:09:55 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.929, 位置: [1145, 233, 1425, 526]
2025-08-04 17:09:55 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:09:55 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.544, 位置: [1099, 509, 1342, 710]
2025-08-04 17:09:55 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 8: 检测到 2 个对象
2025-08-04 17:09:55 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:09:55 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.905, 位置: [1153, 231, 1426, 528]
2025-08-04 17:09:55 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:09:55 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.500, 位置: [1099, 509, 1341, 710]
2025-08-04 17:09:55 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 9: 检测到 1 个对象
2025-08-04 17:09:55 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:09:55 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.853, 位置: [1163, 236, 1429, 539]
2025-08-04 17:09:55 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 10: 检测到 1 个对象
2025-08-04 17:09:55 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:09:55 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.810, 位置: [1165, 234, 1430, 540]
2025-08-04 17:09:56 | INFO     | src.detection.video_capture:get_frame_generator:127 | 视频文件读取完毕
2025-08-04 17:09:56 | INFO     | src.detection.video_capture:release:203 | 视频捕获资源已释放
2025-08-04 17:09:56 | INFO     | src.state_machine.action_detector:_cleanup:434 | 检测完成 - 总帧数: 42, 总运行时间: 2.7s, 平均FPS: 15.4, 检测到的动作数: 0
2025-08-04 17:09:56 | INFO     | __main__:main:261 | ============================================================
2025-08-04 17:09:56 | INFO     | __main__:main:262 | 👋 程序结束
2025-08-04 17:09:56 | INFO     | __main__:main:263 | ============================================================
2025-08-04 17:10:24 | INFO     | src.utils.logger:setup_logger:55 | 日志系统初始化完成，级别: INFO
2025-08-04 17:10:24 | INFO     | src.utils.logger:setup_logger:57 | 日志文件: logs/action_detection.log
2025-08-04 17:10:24 | INFO     | __main__:main:170 | ============================================================
2025-08-04 17:10:24 | INFO     | __main__:main:171 | 🚀 YOLOv8实时动作检测系统启动
2025-08-04 17:10:24 | INFO     | __main__:main:172 | ============================================================
2025-08-04 17:10:24 | INFO     | __main__:main:178 | 使用命令行指定的视频文件: /home/<USER>/wolong_ws/yolo_detect_ws/data/test_videos/short_video_for_test.mp4
2025-08-04 17:10:24 | INFO     | __main__:main:189 | 📹 视频源: /home/<USER>/wolong_ws/yolo_detect_ws/data/test_videos/short_video_for_test.mp4
2025-08-04 17:10:24 | INFO     | __main__:main:190 | 🤖 模型路径: yolo_training/yolov8n_custom2/weights/best.pt
2025-08-04 17:10:24 | INFO     | __main__:main:191 | 📁 输出目录: output/detected_actions
2025-08-04 17:10:24 | INFO     | __main__:main:192 | 🎯 置信度阈值: 0.5
2025-08-04 17:10:24 | INFO     | __main__:main:193 | 📊 交互阈值 - IoU: 0.1, 距离: 50px
2025-08-04 17:10:24 | INFO     | __main__:main:194 | ⏰ 冷却时间: 3.0秒, 动作持续时间: 0.5秒
2025-08-04 17:10:24 | INFO     | src.detection.detector:_get_device:55 | torch.cuda.is_available(): True
2025-08-04 17:10:24 | INFO     | src.detection.detector:_get_device:56 | torch.cuda.device_count(): 1
2025-08-04 17:10:24 | INFO     | src.detection.detector:_get_device:62 | 检测到GPU: NVIDIA GeForce RTX 4060 Laptop GPU (7.6GB)
2025-08-04 17:10:24 | INFO     | src.detection.detector:_get_device:66 | 使用GPU设备: cuda:0
2025-08-04 17:10:24 | INFO     | src.detection.detector:_get_device:88 | 使用设备: 0
2025-08-04 17:10:24 | INFO     | src.detection.detector:_get_device:97 | GPU缓存已清理
2025-08-04 17:10:25 | INFO     | src.detection.detector:_get_device:101 | GPU内存分配策略已设置
2025-08-04 17:10:25 | INFO     | src.detection.detector:_load_model:117 | 加载模型: yolo_training/yolov8n_custom2/weights/best.pt
2025-08-04 17:10:25 | INFO     | src.detection.detector:_load_model:123 | 将模型移动到GPU设备: 0
2025-08-04 17:10:25 | INFO     | src.detection.detector:_load_model:129 | 模型已成功移动到GPU
2025-08-04 17:10:25 | INFO     | src.detection.detector:_load_model:138 | 检测类别: {0: 'blue_box', 1: 'upper_yellow_box', 2: 'lower_yellow_box', 3: 'hand'}
2025-08-04 17:10:25 | INFO     | src.detection.video_capture:_initialize_capture:52 | 初始化视频文件: /home/<USER>/wolong_ws/yolo_detect_ws/data/test_videos/short_video_for_test.mp4
2025-08-04 17:10:25 | INFO     | src.detection.video_capture:_log_video_info:74 | 视频信息 - 分辨率: 1442x898, FPS: 25.02
2025-08-04 17:10:25 | INFO     | src.detection.video_capture:_log_video_info:79 | 视频时长: 60.36秒, 总帧数: 1510
2025-08-04 17:10:25 | INFO     | src.state_machine.action_state_machine:__init__:73 | 状态机初始化完成，初始状态: idle
2025-08-04 17:10:25 | INFO     | src.utils.video_buffer:__init__:33 | 视频缓存器初始化完成，缓存大小: 31帧，帧率: 30.0fps
2025-08-04 17:10:25 | INFO     | src.utils.video_buffer:__init__:168 | 确保目录存在: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_ok
2025-08-04 17:10:25 | INFO     | src.utils.video_buffer:__init__:168 | 确保目录存在: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_nok_electric
2025-08-04 17:10:25 | INFO     | src.utils.video_buffer:__init__:168 | 确保目录存在: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_nok_appearence
2025-08-04 17:10:25 | INFO     | src.state_machine.action_detector:__init__:96 | 视频保存功能已启用，缓存大小: 31帧
2025-08-04 17:10:25 | INFO     | src.state_machine.action_detector:__init__:120 | 动作检测器初始化完成
2025-08-04 17:10:25 | INFO     | __main__:main:226 | 🖥️  推理设备: 0
2025-08-04 17:10:25 | INFO     | __main__:main:237 | 💡 控制提示:
2025-08-04 17:10:25 | INFO     | __main__:main:238 |    按 'q' 或 ESC 退出程序
2025-08-04 17:10:25 | INFO     | __main__:main:239 |    按 'r' 重置状态机
2025-08-04 17:10:25 | INFO     | __main__:main:240 |    按 'd' 切换调试模式
2025-08-04 17:10:25 | INFO     | __main__:main:242 | 🔍 开始检测...
2025-08-04 17:10:25 | INFO     | __main__:main:243 | ------------------------------------------------------------
2025-08-04 17:10:25 | INFO     | src.state_machine.action_detector:run:139 | 开始运行动作检测
2025-08-04 17:10:26 | INFO     | src.detection.detector:detect:223 | 检测调试 #1: 原始检测数=2, 过滤后检测数=2, 置信度阈值=0.5
2025-08-04 17:10:26 | INFO     | src.detection.detector:detect:225 | 推理时间: 1232.6ms, 平均: 1232.6ms
2025-08-04 17:10:26 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 17:10:26 | INFO     | src.detection.detector:detect:233 | 原始类别: [2.0, 1.0]
2025-08-04 17:10:26 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.8734681606292725, 0.8511121273040771]
2025-08-04 17:10:26 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 1: 检测到 2 个对象
2025-08-04 17:10:26 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:10:26 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.851, 位置: [1146, 236, 1421, 522]
2025-08-04 17:10:26 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:10:26 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.873, 位置: [1102, 515, 1342, 707]
2025-08-04 17:10:26 | INFO     | src.detection.detector:detect:223 | 检测调试 #2: 原始检测数=2, 过滤后检测数=2, 置信度阈值=0.5
2025-08-04 17:10:26 | INFO     | src.detection.detector:detect:225 | 推理时间: 9.4ms, 平均: 621.0ms
2025-08-04 17:10:26 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 17:10:26 | INFO     | src.detection.detector:detect:233 | 原始类别: [1.0, 2.0]
2025-08-04 17:10:26 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.8752053380012512, 0.870661199092865]
2025-08-04 17:10:26 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 2: 检测到 2 个对象
2025-08-04 17:10:26 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:10:26 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.875, 位置: [1146, 237, 1420, 522]
2025-08-04 17:10:26 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:10:26 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.871, 位置: [1102, 514, 1342, 706]
2025-08-04 17:10:26 | INFO     | src.detection.detector:detect:223 | 检测调试 #3: 原始检测数=2, 过滤后检测数=2, 置信度阈值=0.5
2025-08-04 17:10:26 | INFO     | src.detection.detector:detect:225 | 推理时间: 7.8ms, 平均: 416.6ms
2025-08-04 17:10:26 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 17:10:26 | INFO     | src.detection.detector:detect:233 | 原始类别: [1.0, 2.0]
2025-08-04 17:10:26 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.8700747489929199, 0.861918032169342]
2025-08-04 17:10:26 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 3: 检测到 2 个对象
2025-08-04 17:10:26 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:10:26 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.870, 位置: [1145, 237, 1420, 522]
2025-08-04 17:10:26 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:10:26 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.862, 位置: [1102, 515, 1342, 707]
2025-08-04 17:10:26 | INFO     | src.detection.detector:detect:223 | 检测调试 #4: 原始检测数=2, 过滤后检测数=2, 置信度阈值=0.5
2025-08-04 17:10:26 | INFO     | src.detection.detector:detect:225 | 推理时间: 9.1ms, 平均: 314.7ms
2025-08-04 17:10:26 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 17:10:26 | INFO     | src.detection.detector:detect:233 | 原始类别: [1.0, 2.0]
2025-08-04 17:10:26 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.8732701539993286, 0.8566346168518066]
2025-08-04 17:10:26 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 4: 检测到 2 个对象
2025-08-04 17:10:26 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:10:26 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.873, 位置: [1145, 237, 1420, 522]
2025-08-04 17:10:26 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:10:26 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.857, 位置: [1102, 515, 1342, 707]
2025-08-04 17:10:26 | INFO     | src.detection.detector:detect:223 | 检测调试 #5: 原始检测数=2, 过滤后检测数=2, 置信度阈值=0.5
2025-08-04 17:10:26 | INFO     | src.detection.detector:detect:225 | 推理时间: 6.5ms, 平均: 253.1ms
2025-08-04 17:10:26 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 17:10:26 | INFO     | src.detection.detector:detect:233 | 原始类别: [2.0, 1.0]
2025-08-04 17:10:26 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.8743568062782288, 0.8706481456756592]
2025-08-04 17:10:26 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 5: 检测到 2 个对象
2025-08-04 17:10:26 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:10:26 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.871, 位置: [1145, 237, 1420, 522]
2025-08-04 17:10:26 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:10:26 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.874, 位置: [1102, 514, 1342, 706]
2025-08-04 17:10:26 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 6: 检测到 2 个对象
2025-08-04 17:10:26 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:10:26 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.867, 位置: [1145, 237, 1420, 522]
2025-08-04 17:10:26 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:10:26 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.872, 位置: [1102, 514, 1342, 706]
2025-08-04 17:10:26 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 7: 检测到 2 个对象
2025-08-04 17:10:26 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:10:26 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.864, 位置: [1145, 237, 1420, 522]
2025-08-04 17:10:26 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:10:26 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.871, 位置: [1102, 514, 1342, 706]
2025-08-04 17:10:26 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 8: 检测到 2 个对象
2025-08-04 17:10:26 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:10:26 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.862, 位置: [1145, 237, 1420, 522]
2025-08-04 17:10:26 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:10:26 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.868, 位置: [1102, 514, 1342, 706]
2025-08-04 17:10:26 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 9: 检测到 2 个对象
2025-08-04 17:10:26 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:10:26 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.869, 位置: [1145, 237, 1420, 522]
2025-08-04 17:10:26 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:10:26 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.864, 位置: [1102, 514, 1342, 706]
2025-08-04 17:10:26 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 10: 检测到 2 个对象
2025-08-04 17:10:26 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:10:26 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.874, 位置: [1146, 237, 1420, 522]
2025-08-04 17:10:26 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:10:26 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.868, 位置: [1102, 514, 1342, 706]
2025-08-04 17:10:28 | INFO     | src.state_machine.action_detector:run:186 | 检测到键盘中断，正在退出...
2025-08-04 17:10:28 | INFO     | src.detection.video_capture:release:203 | 视频捕获资源已释放
2025-08-04 17:10:28 | INFO     | src.state_machine.action_detector:_cleanup:434 | 检测完成 - 总帧数: 65, 总运行时间: 3.4s, 平均FPS: 18.9, 检测到的动作数: 0
2025-08-04 17:10:28 | INFO     | __main__:main:261 | ============================================================
2025-08-04 17:10:28 | INFO     | __main__:main:262 | 👋 程序结束
2025-08-04 17:10:28 | INFO     | __main__:main:263 | ============================================================
2025-08-04 17:10:33 | INFO     | src.utils.logger:setup_logger:55 | 日志系统初始化完成，级别: INFO
2025-08-04 17:10:33 | INFO     | src.utils.logger:setup_logger:57 | 日志文件: logs/action_detection.log
2025-08-04 17:10:33 | INFO     | __main__:main:170 | ============================================================
2025-08-04 17:10:33 | INFO     | __main__:main:171 | 🚀 YOLOv8实时动作检测系统启动
2025-08-04 17:10:33 | INFO     | __main__:main:172 | ============================================================
2025-08-04 17:10:33 | INFO     | __main__:main:178 | 使用命令行指定的视频文件: /home/<USER>/wolong_ws/yolo_detect_ws/data/test_videos/test_video_long.mp4
2025-08-04 17:10:33 | INFO     | __main__:main:189 | 📹 视频源: /home/<USER>/wolong_ws/yolo_detect_ws/data/test_videos/test_video_long.mp4
2025-08-04 17:10:33 | INFO     | __main__:main:190 | 🤖 模型路径: yolo_training/yolov8n_custom2/weights/best.pt
2025-08-04 17:10:33 | INFO     | __main__:main:191 | 📁 输出目录: output/detected_actions
2025-08-04 17:10:33 | INFO     | __main__:main:192 | 🎯 置信度阈值: 0.5
2025-08-04 17:10:33 | INFO     | __main__:main:193 | 📊 交互阈值 - IoU: 0.1, 距离: 50px
2025-08-04 17:10:33 | INFO     | __main__:main:194 | ⏰ 冷却时间: 3.0秒, 动作持续时间: 0.5秒
2025-08-04 17:10:33 | INFO     | src.detection.detector:_get_device:55 | torch.cuda.is_available(): True
2025-08-04 17:10:33 | INFO     | src.detection.detector:_get_device:56 | torch.cuda.device_count(): 1
2025-08-04 17:10:33 | INFO     | src.detection.detector:_get_device:62 | 检测到GPU: NVIDIA GeForce RTX 4060 Laptop GPU (7.6GB)
2025-08-04 17:10:33 | INFO     | src.detection.detector:_get_device:66 | 使用GPU设备: cuda:0
2025-08-04 17:10:33 | INFO     | src.detection.detector:_get_device:88 | 使用设备: 0
2025-08-04 17:10:33 | INFO     | src.detection.detector:_get_device:97 | GPU缓存已清理
2025-08-04 17:10:33 | INFO     | src.detection.detector:_get_device:101 | GPU内存分配策略已设置
2025-08-04 17:10:33 | INFO     | src.detection.detector:_load_model:117 | 加载模型: yolo_training/yolov8n_custom2/weights/best.pt
2025-08-04 17:10:33 | INFO     | src.detection.detector:_load_model:123 | 将模型移动到GPU设备: 0
2025-08-04 17:10:33 | INFO     | src.detection.detector:_load_model:129 | 模型已成功移动到GPU
2025-08-04 17:10:33 | INFO     | src.detection.detector:_load_model:138 | 检测类别: {0: 'blue_box', 1: 'upper_yellow_box', 2: 'lower_yellow_box', 3: 'hand'}
2025-08-04 17:10:33 | INFO     | src.detection.video_capture:_initialize_capture:52 | 初始化视频文件: /home/<USER>/wolong_ws/yolo_detect_ws/data/test_videos/test_video_long.mp4
2025-08-04 17:10:33 | INFO     | src.detection.video_capture:_log_video_info:74 | 视频信息 - 分辨率: 1442x898, FPS: 25.00
2025-08-04 17:10:33 | INFO     | src.detection.video_capture:_log_video_info:79 | 视频时长: 304.00秒, 总帧数: 7600
2025-08-04 17:10:33 | INFO     | src.state_machine.action_state_machine:__init__:73 | 状态机初始化完成，初始状态: idle
2025-08-04 17:10:33 | INFO     | src.utils.video_buffer:__init__:33 | 视频缓存器初始化完成，缓存大小: 31帧，帧率: 30.0fps
2025-08-04 17:10:33 | INFO     | src.utils.video_buffer:__init__:168 | 确保目录存在: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_ok
2025-08-04 17:10:33 | INFO     | src.utils.video_buffer:__init__:168 | 确保目录存在: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_nok_electric
2025-08-04 17:10:33 | INFO     | src.utils.video_buffer:__init__:168 | 确保目录存在: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_nok_appearence
2025-08-04 17:10:33 | INFO     | src.state_machine.action_detector:__init__:96 | 视频保存功能已启用，缓存大小: 31帧
2025-08-04 17:10:33 | INFO     | src.state_machine.action_detector:__init__:120 | 动作检测器初始化完成
2025-08-04 17:10:33 | INFO     | __main__:main:226 | 🖥️  推理设备: 0
2025-08-04 17:10:33 | INFO     | __main__:main:237 | 💡 控制提示:
2025-08-04 17:10:33 | INFO     | __main__:main:238 |    按 'q' 或 ESC 退出程序
2025-08-04 17:10:33 | INFO     | __main__:main:239 |    按 'r' 重置状态机
2025-08-04 17:10:33 | INFO     | __main__:main:240 |    按 'd' 切换调试模式
2025-08-04 17:10:33 | INFO     | __main__:main:242 | 🔍 开始检测...
2025-08-04 17:10:33 | INFO     | __main__:main:243 | ------------------------------------------------------------
2025-08-04 17:10:33 | INFO     | src.state_machine.action_detector:run:139 | 开始运行动作检测
2025-08-04 17:10:35 | INFO     | src.detection.detector:detect:223 | 检测调试 #1: 原始检测数=3, 过滤后检测数=3, 置信度阈值=0.5
2025-08-04 17:10:35 | INFO     | src.detection.detector:detect:225 | 推理时间: 1273.2ms, 平均: 1273.2ms
2025-08-04 17:10:35 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 17:10:35 | INFO     | src.detection.detector:detect:233 | 原始类别: [2.0, 0.0, 1.0]
2025-08-04 17:10:35 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.9483245611190796, 0.9287644624710083, 0.9003223180770874]
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 1: 检测到 3 个对象
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.929, 位置: [0, 586, 491, 898]
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.900, 位置: [1168, 223, 1426, 518]
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.948, 位置: [1105, 507, 1348, 711]
2025-08-04 17:10:35 | INFO     | src.detection.detector:detect:223 | 检测调试 #2: 原始检测数=3, 过滤后检测数=3, 置信度阈值=0.5
2025-08-04 17:10:35 | INFO     | src.detection.detector:detect:225 | 推理时间: 7.8ms, 平均: 640.5ms
2025-08-04 17:10:35 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 17:10:35 | INFO     | src.detection.detector:detect:233 | 原始类别: [2.0, 0.0, 1.0]
2025-08-04 17:10:35 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.9426160454750061, 0.92695152759552, 0.9040740132331848]
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 2: 检测到 3 个对象
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.927, 位置: [1, 596, 490, 898]
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.904, 位置: [1169, 223, 1426, 518]
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.943, 位置: [1104, 507, 1348, 711]
2025-08-04 17:10:35 | INFO     | src.detection.detector:detect:223 | 检测调试 #3: 原始检测数=3, 过滤后检测数=3, 置信度阈值=0.5
2025-08-04 17:10:35 | INFO     | src.detection.detector:detect:225 | 推理时间: 8.9ms, 平均: 430.0ms
2025-08-04 17:10:35 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 17:10:35 | INFO     | src.detection.detector:detect:233 | 原始类别: [2.0, 0.0, 1.0]
2025-08-04 17:10:35 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.9422558546066284, 0.9203870296478271, 0.9028117656707764]
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 3: 检测到 3 个对象
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.920, 位置: [1, 596, 490, 898]
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.903, 位置: [1169, 223, 1426, 518]
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.942, 位置: [1104, 507, 1348, 711]
2025-08-04 17:10:35 | INFO     | src.detection.detector:detect:223 | 检测调试 #4: 原始检测数=3, 过滤后检测数=3, 置信度阈值=0.5
2025-08-04 17:10:35 | INFO     | src.detection.detector:detect:225 | 推理时间: 6.5ms, 平均: 324.1ms
2025-08-04 17:10:35 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 17:10:35 | INFO     | src.detection.detector:detect:233 | 原始类别: [2.0, 0.0, 1.0]
2025-08-04 17:10:35 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.9385360479354858, 0.9256897568702698, 0.9024756550788879]
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 4: 检测到 3 个对象
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.926, 位置: [1, 596, 490, 898]
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.902, 位置: [1169, 223, 1426, 518]
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.939, 位置: [1105, 507, 1348, 711]
2025-08-04 17:10:35 | INFO     | src.detection.detector:detect:223 | 检测调试 #5: 原始检测数=3, 过滤后检测数=3, 置信度阈值=0.5
2025-08-04 17:10:35 | INFO     | src.detection.detector:detect:225 | 推理时间: 7.9ms, 平均: 260.9ms
2025-08-04 17:10:35 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 17:10:35 | INFO     | src.detection.detector:detect:233 | 原始类别: [2.0, 0.0, 1.0]
2025-08-04 17:10:35 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.9389451146125793, 0.9274168610572815, 0.9030306935310364]
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 5: 检测到 3 个对象
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.927, 位置: [1, 596, 490, 898]
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.903, 位置: [1169, 223, 1426, 518]
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.939, 位置: [1104, 507, 1348, 711]
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 6: 检测到 3 个对象
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.926, 位置: [1, 596, 490, 898]
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.903, 位置: [1168, 223, 1426, 518]
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.936, 位置: [1104, 507, 1348, 711]
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 7: 检测到 3 个对象
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.926, 位置: [1, 596, 490, 898]
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.902, 位置: [1169, 223, 1426, 518]
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.943, 位置: [1105, 507, 1348, 711]
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 8: 检测到 3 个对象
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.923, 位置: [1, 596, 490, 898]
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.901, 位置: [1169, 223, 1426, 518]
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.943, 位置: [1105, 507, 1348, 711]
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 9: 检测到 3 个对象
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.923, 位置: [1, 596, 489, 898]
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.899, 位置: [1169, 223, 1426, 518]
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.943, 位置: [1105, 507, 1348, 711]
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 10: 检测到 3 个对象
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.921, 位置: [1, 596, 489, 898]
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.900, 位置: [1168, 223, 1426, 518]
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:10:35 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.942, 位置: [1105, 507, 1348, 711]
2025-08-04 17:10:38 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 100: 检测到 4 个对象
2025-08-04 17:10:38 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - hand: 1 个
2025-08-04 17:10:38 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.574, 位置: [296, 601, 439, 718]
2025-08-04 17:10:38 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:10:38 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.865, 位置: [0, 572, 488, 898]
2025-08-04 17:10:38 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:10:38 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.906, 位置: [1168, 224, 1426, 518]
2025-08-04 17:10:38 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:10:38 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.941, 位置: [1104, 507, 1348, 711]
2025-08-04 17:10:38 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与blue_box交互，IoU: 0.105, 距离: 144.9
2025-08-04 17:10:38 | INFO     | src.state_machine.action_state_machine:_handle_detecting_state:130 | 手部与blue_box的交互中断
2025-08-04 17:10:41 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 200: 检测到 3 个对象
2025-08-04 17:10:41 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:10:41 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.930, 位置: [0, 586, 492, 898]
2025-08-04 17:10:41 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:10:41 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.900, 位置: [1168, 224, 1426, 518]
2025-08-04 17:10:41 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:10:41 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.944, 位置: [1106, 507, 1348, 711]
2025-08-04 17:10:44 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与blue_box交互，IoU: 0.135, 距离: 157.3
2025-08-04 17:10:44 | SUCCESS  | src.state_machine.action_state_machine:_handle_detecting_state:155 | 检测到ok_action！第1次动作，持续时间: 0.50秒
2025-08-04 17:10:44 | SUCCESS  | src.state_machine.action_detector:_handle_action_detected:198 | 检测到动作: action_1754298644_1 (类型: ok_action)
2025-08-04 17:10:45 | SUCCESS  | src.utils.video_buffer:save_action_video:117 | 动作视频已保存: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_ok/action_1754298644_1_20250804_171044.mp4 (31帧)
2025-08-04 17:10:45 | SUCCESS  | src.state_machine.action_detector:_handle_action_detected:206 | 动作视频已保存: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_ok/action_1754298644_1_20250804_171044.mp4
2025-08-04 17:10:45 | ERROR    | src.state_machine.action_detector:_save_action_image:246 | 保存动作图像失败: 'tracked_object'
2025-08-04 17:10:45 | SUCCESS  | __main__:action_callback:132 | 🎯 检测到动作 #action_1754298644_1
2025-08-04 17:10:45 | INFO     | __main__:action_callback:133 |    动作类型: ✅ OK动作 - 产品放入蓝色箱子
2025-08-04 17:10:45 | INFO     | __main__:action_callback:134 |    容器类型: blue_box
2025-08-04 17:10:45 | INFO     | __main__:action_callback:135 |    时间戳: 17:10:44
2025-08-04 17:10:45 | INFO     | __main__:action_callback:139 |    交互时长: 0.50秒
2025-08-04 17:10:45 | INFO     | __main__:action_callback:140 |    IoU: 0.128
2025-08-04 17:10:45 | INFO     | __main__:action_callback:141 |    距离: 18.9px
2025-08-04 17:10:45 | INFO     | __main__:action_callback:142 |    置信度: 0.644
2025-08-04 17:10:45 | INFO     | __main__:action_callback:145 |    保存路径: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_ok/
2025-08-04 17:10:45 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 300: 检测到 3 个对象
2025-08-04 17:10:45 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:10:45 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.946, 位置: [4, 616, 478, 898]
2025-08-04 17:10:45 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:10:45 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.903, 位置: [1169, 223, 1426, 518]
2025-08-04 17:10:45 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:10:45 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.933, 位置: [1103, 507, 1348, 710]
2025-08-04 17:10:45 | INFO     | src.state_machine.action_detector:_log_statistics:396 | 统计信息 - 帧数: 300, 运行时间: 11.5s, FPS: 26.1, 状态: idle, 动作数: 1
2025-08-04 17:10:45 | INFO     | src.state_machine.action_detector:_log_statistics:404 | 检测统计 - 有检测的帧: 300/300 (100.0%), 总检测数: 933, 手: 33, 蓝色箱子: 300, 上层黄色箱子: 300, 下层黄色箱子: 300
2025-08-04 17:10:45 | INFO     | src.state_machine.action_detector:_log_statistics:414 | 性能统计 - 平均推理时间: 15.5ms, 估计FPS: 64.5, 设备: 0
2025-08-04 17:10:45 | INFO     | src.state_machine.action_detector:_log_statistics:419 | GPU内存 - 已分配: 43.5MB, 已缓存: 90.0MB
2025-08-04 17:10:48 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 400: 检测到 3 个对象
2025-08-04 17:10:48 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:10:48 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.913, 位置: [1, 597, 490, 898]
2025-08-04 17:10:48 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:10:48 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.906, 位置: [1169, 223, 1426, 518]
2025-08-04 17:10:48 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:10:48 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.944, 位置: [1105, 508, 1348, 711]
2025-08-04 17:10:51 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 500: 检测到 3 个对象
2025-08-04 17:10:51 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:10:51 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.924, 位置: [2, 597, 490, 898]
2025-08-04 17:10:51 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:10:51 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.900, 位置: [1168, 224, 1426, 518]
2025-08-04 17:10:51 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:10:51 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.940, 位置: [1105, 507, 1348, 710]
2025-08-04 17:10:55 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 600: 检测到 3 个对象
2025-08-04 17:10:55 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:10:55 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.924, 位置: [1, 597, 491, 898]
2025-08-04 17:10:55 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:10:55 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.876, 位置: [1169, 224, 1426, 518]
2025-08-04 17:10:55 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:10:55 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.948, 位置: [1104, 507, 1348, 711]
2025-08-04 17:10:55 | INFO     | src.state_machine.action_detector:_log_statistics:396 | 统计信息 - 帧数: 600, 运行时间: 21.5s, FPS: 27.9, 状态: idle, 动作数: 1
2025-08-04 17:10:55 | INFO     | src.state_machine.action_detector:_log_statistics:404 | 检测统计 - 有检测的帧: 600/600 (100.0%), 总检测数: 1833, 手: 33, 蓝色箱子: 600, 上层黄色箱子: 600, 下层黄色箱子: 600
2025-08-04 17:10:55 | INFO     | src.state_machine.action_detector:_log_statistics:414 | 性能统计 - 平均推理时间: 13.3ms, 估计FPS: 75.3, 设备: 0
2025-08-04 17:10:55 | INFO     | src.state_machine.action_detector:_log_statistics:419 | GPU内存 - 已分配: 43.5MB, 已缓存: 90.0MB
2025-08-04 17:10:58 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 700: 检测到 3 个对象
2025-08-04 17:10:58 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:10:58 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.929, 位置: [1, 596, 490, 898]
2025-08-04 17:10:58 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:10:58 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.892, 位置: [1168, 223, 1425, 518]
2025-08-04 17:10:58 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:10:58 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.942, 位置: [1105, 508, 1348, 711]
2025-08-04 17:11:01 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 800: 检测到 3 个对象
2025-08-04 17:11:01 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:11:01 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.921, 位置: [1, 596, 490, 898]
2025-08-04 17:11:01 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:11:01 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.905, 位置: [1168, 223, 1426, 518]
2025-08-04 17:11:01 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:11:01 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.943, 位置: [1106, 508, 1348, 711]
2025-08-04 17:11:05 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 900: 检测到 3 个对象
2025-08-04 17:11:05 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:11:05 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.925, 位置: [1, 597, 490, 898]
2025-08-04 17:11:05 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:11:05 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.905, 位置: [1168, 223, 1426, 518]
2025-08-04 17:11:05 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:11:05 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.928, 位置: [1105, 507, 1348, 711]
2025-08-04 17:11:05 | INFO     | src.state_machine.action_detector:_log_statistics:396 | 统计信息 - 帧数: 900, 运行时间: 31.5s, FPS: 28.5, 状态: idle, 动作数: 1
2025-08-04 17:11:05 | INFO     | src.state_machine.action_detector:_log_statistics:404 | 检测统计 - 有检测的帧: 900/900 (100.0%), 总检测数: 2733, 手: 33, 蓝色箱子: 900, 上层黄色箱子: 900, 下层黄色箱子: 900
2025-08-04 17:11:05 | INFO     | src.state_machine.action_detector:_log_statistics:414 | 性能统计 - 平均推理时间: 12.7ms, 估计FPS: 78.9, 设备: 0
2025-08-04 17:11:05 | INFO     | src.state_machine.action_detector:_log_statistics:419 | GPU内存 - 已分配: 43.5MB, 已缓存: 90.0MB
2025-08-04 17:11:08 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 1000: 检测到 3 个对象
2025-08-04 17:11:08 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:11:08 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.926, 位置: [1, 596, 490, 898]
2025-08-04 17:11:08 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:11:08 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.906, 位置: [1168, 224, 1426, 518]
2025-08-04 17:11:08 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:11:08 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.930, 位置: [1107, 507, 1348, 711]
2025-08-04 17:11:11 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 1100: 检测到 3 个对象
2025-08-04 17:11:11 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:11:11 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.923, 位置: [1, 596, 490, 898]
2025-08-04 17:11:11 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:11:11 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.907, 位置: [1168, 223, 1426, 518]
2025-08-04 17:11:11 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:11:11 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.939, 位置: [1104, 508, 1348, 710]
2025-08-04 17:11:15 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 1200: 检测到 3 个对象
2025-08-04 17:11:15 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:11:15 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.926, 位置: [1, 596, 490, 898]
2025-08-04 17:11:15 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:11:15 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.882, 位置: [1169, 223, 1426, 518]
2025-08-04 17:11:15 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:11:15 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.934, 位置: [1108, 508, 1348, 711]
2025-08-04 17:11:15 | INFO     | src.state_machine.action_detector:_log_statistics:396 | 统计信息 - 帧数: 1200, 运行时间: 41.6s, FPS: 28.9, 状态: idle, 动作数: 1
2025-08-04 17:11:15 | INFO     | src.state_machine.action_detector:_log_statistics:404 | 检测统计 - 有检测的帧: 1200/1200 (100.0%), 总检测数: 3634, 手: 34, 蓝色箱子: 1200, 上层黄色箱子: 1200, 下层黄色箱子: 1200
2025-08-04 17:11:15 | INFO     | src.state_machine.action_detector:_log_statistics:414 | 性能统计 - 平均推理时间: 12.4ms, 估计FPS: 80.4, 设备: 0
2025-08-04 17:11:15 | INFO     | src.state_machine.action_detector:_log_statistics:419 | GPU内存 - 已分配: 43.5MB, 已缓存: 90.0MB
2025-08-04 17:11:18 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 1300: 检测到 3 个对象
2025-08-04 17:11:18 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:11:18 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.924, 位置: [1, 596, 491, 898]
2025-08-04 17:11:18 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:11:18 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.880, 位置: [1168, 223, 1426, 518]
2025-08-04 17:11:18 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:11:18 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.930, 位置: [1107, 508, 1348, 711]
2025-08-04 17:11:22 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 1400: 检测到 3 个对象
2025-08-04 17:11:22 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:11:22 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.919, 位置: [1, 596, 491, 898]
2025-08-04 17:11:22 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:11:22 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.884, 位置: [1168, 223, 1425, 518]
2025-08-04 17:11:22 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:11:22 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.934, 位置: [1106, 508, 1348, 711]
2025-08-04 17:11:25 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 1500: 检测到 4 个对象
2025-08-04 17:11:25 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - hand: 1 个
2025-08-04 17:11:25 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.661, 位置: [896, 479, 1044, 563]
2025-08-04 17:11:25 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:11:25 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.935, 位置: [1, 596, 491, 898]
2025-08-04 17:11:25 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:11:25 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.889, 位置: [1168, 223, 1426, 518]
2025-08-04 17:11:25 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:11:25 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.937, 位置: [1107, 508, 1348, 711]
2025-08-04 17:11:25 | INFO     | src.state_machine.action_detector:_log_statistics:396 | 统计信息 - 帧数: 1500, 运行时间: 51.6s, FPS: 29.1, 状态: idle, 动作数: 1
2025-08-04 17:11:25 | INFO     | src.state_machine.action_detector:_log_statistics:404 | 检测统计 - 有检测的帧: 1500/1500 (100.0%), 总检测数: 4582, 手: 82, 蓝色箱子: 1500, 上层黄色箱子: 1500, 下层黄色箱子: 1500
2025-08-04 17:11:25 | INFO     | src.state_machine.action_detector:_log_statistics:414 | 性能统计 - 平均推理时间: 12.3ms, 估计FPS: 81.3, 设备: 0
2025-08-04 17:11:25 | INFO     | src.state_machine.action_detector:_log_statistics:419 | GPU内存 - 已分配: 43.5MB, 已缓存: 90.0MB
2025-08-04 17:11:28 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 1600: 检测到 3 个对象
2025-08-04 17:11:28 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:11:28 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.928, 位置: [1, 597, 490, 898]
2025-08-04 17:11:28 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:11:28 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.897, 位置: [1169, 223, 1426, 518]
2025-08-04 17:11:28 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:11:28 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.940, 位置: [1107, 507, 1348, 711]
2025-08-04 17:11:29 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与blue_box交互，IoU: 0.121, 距离: 145.2
2025-08-04 17:11:29 | SUCCESS  | src.state_machine.action_state_machine:_handle_detecting_state:155 | 检测到ok_action！第2次动作，持续时间: 0.50秒
2025-08-04 17:11:29 | SUCCESS  | src.state_machine.action_detector:_handle_action_detected:198 | 检测到动作: action_1754298689_2 (类型: ok_action)
2025-08-04 17:11:29 | SUCCESS  | src.utils.video_buffer:save_action_video:117 | 动作视频已保存: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_ok/action_1754298689_2_20250804_171129.mp4 (31帧)
2025-08-04 17:11:29 | SUCCESS  | src.state_machine.action_detector:_handle_action_detected:206 | 动作视频已保存: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_ok/action_1754298689_2_20250804_171129.mp4
2025-08-04 17:11:29 | ERROR    | src.state_machine.action_detector:_save_action_image:246 | 保存动作图像失败: 'tracked_object'
2025-08-04 17:11:29 | SUCCESS  | __main__:action_callback:132 | 🎯 检测到动作 #action_1754298689_2
2025-08-04 17:11:29 | INFO     | __main__:action_callback:133 |    动作类型: ✅ OK动作 - 产品放入蓝色箱子
2025-08-04 17:11:29 | INFO     | __main__:action_callback:134 |    容器类型: blue_box
2025-08-04 17:11:29 | INFO     | __main__:action_callback:135 |    时间戳: 17:11:29
2025-08-04 17:11:29 | INFO     | __main__:action_callback:139 |    交互时长: 0.50秒
2025-08-04 17:11:29 | INFO     | __main__:action_callback:140 |    IoU: 0.119
2025-08-04 17:11:29 | INFO     | __main__:action_callback:141 |    距离: 63.3px
2025-08-04 17:11:29 | INFO     | __main__:action_callback:142 |    置信度: 0.637
2025-08-04 17:11:29 | INFO     | __main__:action_callback:145 |    保存路径: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_ok/
2025-08-04 17:11:32 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 1700: 检测到 3 个对象
2025-08-04 17:11:32 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:11:32 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.938, 位置: [1, 598, 490, 898]
2025-08-04 17:11:32 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:11:32 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.893, 位置: [1169, 223, 1426, 518]
2025-08-04 17:11:32 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:11:32 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.943, 位置: [1104, 508, 1348, 711]
2025-08-04 17:11:35 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 1800: 检测到 4 个对象
2025-08-04 17:11:35 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - hand: 1 个
2025-08-04 17:11:35 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.554, 位置: [880, 456, 1036, 570]
2025-08-04 17:11:35 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:11:35 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.934, 位置: [1, 596, 490, 898]
2025-08-04 17:11:35 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:11:35 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.888, 位置: [1168, 224, 1426, 518]
2025-08-04 17:11:35 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:11:35 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.942, 位置: [1106, 508, 1348, 711]
2025-08-04 17:11:35 | INFO     | src.state_machine.action_detector:_log_statistics:396 | 统计信息 - 帧数: 1800, 运行时间: 61.8s, FPS: 29.1, 状态: idle, 动作数: 2
2025-08-04 17:11:35 | INFO     | src.state_machine.action_detector:_log_statistics:404 | 检测统计 - 有检测的帧: 1800/1800 (100.0%), 总检测数: 5502, 手: 102, 蓝色箱子: 1800, 上层黄色箱子: 1800, 下层黄色箱子: 1800
2025-08-04 17:11:35 | INFO     | src.state_machine.action_detector:_log_statistics:414 | 性能统计 - 平均推理时间: 12.1ms, 估计FPS: 82.3, 设备: 0
2025-08-04 17:11:35 | INFO     | src.state_machine.action_detector:_log_statistics:419 | GPU内存 - 已分配: 43.5MB, 已缓存: 90.0MB
2025-08-04 17:11:38 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 1900: 检测到 3 个对象
2025-08-04 17:11:38 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:11:38 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.930, 位置: [1, 597, 491, 898]
2025-08-04 17:11:38 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:11:38 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.889, 位置: [1165, 223, 1427, 518]
2025-08-04 17:11:38 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:11:38 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.948, 位置: [1104, 508, 1348, 711]
2025-08-04 17:11:42 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 2000: 检测到 3 个对象
2025-08-04 17:11:42 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:11:42 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.935, 位置: [1, 596, 490, 898]
2025-08-04 17:11:42 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:11:42 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.897, 位置: [1169, 223, 1426, 518]
2025-08-04 17:11:42 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:11:42 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.942, 位置: [1105, 507, 1348, 711]
2025-08-04 17:11:43 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与blue_box交互，IoU: 0.104, 距离: 142.1
2025-08-04 17:11:43 | INFO     | src.state_machine.action_state_machine:_handle_detecting_state:130 | 手部与blue_box的交互中断
2025-08-04 17:11:43 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与blue_box交互，IoU: 0.134, 距离: 59.6
2025-08-04 17:11:43 | INFO     | src.state_machine.action_state_machine:_handle_detecting_state:130 | 手部与blue_box的交互中断
2025-08-04 17:11:43 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与blue_box交互，IoU: 0.126, 距离: 63.2
2025-08-04 17:11:44 | INFO     | src.state_machine.action_state_machine:_handle_detecting_state:130 | 手部与blue_box的交互中断
2025-08-04 17:11:45 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 2100: 检测到 3 个对象
2025-08-04 17:11:45 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:11:45 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.935, 位置: [1, 597, 491, 898]
2025-08-04 17:11:45 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:11:45 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.907, 位置: [1168, 223, 1426, 518]
2025-08-04 17:11:45 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:11:45 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.933, 位置: [1107, 507, 1348, 711]
2025-08-04 17:11:45 | INFO     | src.state_machine.action_detector:_log_statistics:396 | 统计信息 - 帧数: 2100, 运行时间: 71.8s, FPS: 29.2, 状态: idle, 动作数: 2
2025-08-04 17:11:45 | INFO     | src.state_machine.action_detector:_log_statistics:404 | 检测统计 - 有检测的帧: 2100/2100 (100.0%), 总检测数: 6436, 手: 136, 蓝色箱子: 2100, 上层黄色箱子: 2100, 下层黄色箱子: 2100
2025-08-04 17:11:45 | INFO     | src.state_machine.action_detector:_log_statistics:414 | 性能统计 - 平均推理时间: 12.1ms, 估计FPS: 82.8, 设备: 0
2025-08-04 17:11:45 | INFO     | src.state_machine.action_detector:_log_statistics:419 | GPU内存 - 已分配: 43.5MB, 已缓存: 90.0MB
2025-08-04 17:11:48 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 2200: 检测到 3 个对象
2025-08-04 17:11:48 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:11:48 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.945, 位置: [1, 596, 490, 898]
2025-08-04 17:11:48 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:11:48 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.900, 位置: [1168, 223, 1426, 518]
2025-08-04 17:11:48 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:11:48 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.951, 位置: [1105, 507, 1348, 710]
2025-08-04 17:11:52 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 2300: 检测到 3 个对象
2025-08-04 17:11:52 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:11:52 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.937, 位置: [0, 587, 492, 898]
2025-08-04 17:11:52 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:11:52 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.898, 位置: [1168, 223, 1426, 518]
2025-08-04 17:11:52 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:11:52 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.931, 位置: [1104, 508, 1348, 711]
2025-08-04 17:11:55 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 2400: 检测到 3 个对象
2025-08-04 17:11:55 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:11:55 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.932, 位置: [1, 597, 490, 898]
2025-08-04 17:11:55 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:11:55 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.880, 位置: [1168, 223, 1426, 518]
2025-08-04 17:11:55 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:11:55 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.935, 位置: [1105, 508, 1348, 711]
2025-08-04 17:11:55 | INFO     | src.state_machine.action_detector:_log_statistics:396 | 统计信息 - 帧数: 2400, 运行时间: 81.9s, FPS: 29.3, 状态: idle, 动作数: 2
2025-08-04 17:11:55 | INFO     | src.state_machine.action_detector:_log_statistics:404 | 检测统计 - 有检测的帧: 2400/2400 (100.0%), 总检测数: 7340, 手: 140, 蓝色箱子: 2400, 上层黄色箱子: 2400, 下层黄色箱子: 2400
2025-08-04 17:11:55 | INFO     | src.state_machine.action_detector:_log_statistics:414 | 性能统计 - 平均推理时间: 12.0ms, 估计FPS: 83.3, 设备: 0
2025-08-04 17:11:55 | INFO     | src.state_machine.action_detector:_log_statistics:419 | GPU内存 - 已分配: 43.5MB, 已缓存: 90.0MB
2025-08-04 17:11:58 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 2500: 检测到 3 个对象
2025-08-04 17:11:58 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:11:58 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.934, 位置: [1, 596, 490, 898]
2025-08-04 17:11:58 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:11:58 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.867, 位置: [1168, 224, 1425, 518]
2025-08-04 17:11:58 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:11:58 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.938, 位置: [1107, 508, 1348, 710]
2025-08-04 17:12:02 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 2600: 检测到 3 个对象
2025-08-04 17:12:02 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:12:02 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.936, 位置: [2, 596, 491, 898]
2025-08-04 17:12:02 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:12:02 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.895, 位置: [1168, 223, 1425, 518]
2025-08-04 17:12:02 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:12:02 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.947, 位置: [1104, 508, 1348, 711]
2025-08-04 17:12:05 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 2700: 检测到 3 个对象
2025-08-04 17:12:05 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:12:05 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.932, 位置: [1, 596, 490, 898]
2025-08-04 17:12:05 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:12:05 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.904, 位置: [1168, 223, 1426, 518]
2025-08-04 17:12:05 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:12:05 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.948, 位置: [1104, 507, 1348, 711]
2025-08-04 17:12:05 | INFO     | src.state_machine.action_detector:_log_statistics:396 | 统计信息 - 帧数: 2700, 运行时间: 91.9s, FPS: 29.4, 状态: idle, 动作数: 2
2025-08-04 17:12:05 | INFO     | src.state_machine.action_detector:_log_statistics:404 | 检测统计 - 有检测的帧: 2700/2700 (100.0%), 总检测数: 8240, 手: 140, 蓝色箱子: 2700, 上层黄色箱子: 2700, 下层黄色箱子: 2700
2025-08-04 17:12:05 | INFO     | src.state_machine.action_detector:_log_statistics:414 | 性能统计 - 平均推理时间: 11.9ms, 估计FPS: 83.9, 设备: 0
2025-08-04 17:12:05 | INFO     | src.state_machine.action_detector:_log_statistics:419 | GPU内存 - 已分配: 43.5MB, 已缓存: 90.0MB
2025-08-04 17:12:08 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 2800: 检测到 3 个对象
2025-08-04 17:12:08 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:12:08 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.931, 位置: [1, 597, 491, 898]
2025-08-04 17:12:08 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:12:08 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.871, 位置: [1168, 224, 1426, 518]
2025-08-04 17:12:08 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:12:08 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.951, 位置: [1105, 508, 1348, 711]
2025-08-04 17:12:12 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 2900: 检测到 3 个对象
2025-08-04 17:12:12 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:12:12 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.937, 位置: [1, 596, 490, 898]
2025-08-04 17:12:12 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:12:12 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.888, 位置: [1168, 224, 1426, 518]
2025-08-04 17:12:12 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:12:12 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.956, 位置: [1107, 508, 1348, 711]
2025-08-04 17:12:15 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 3000: 检测到 3 个对象
2025-08-04 17:12:15 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:12:15 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.932, 位置: [1, 597, 490, 898]
2025-08-04 17:12:15 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:12:15 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.894, 位置: [1168, 223, 1426, 518]
2025-08-04 17:12:15 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:12:15 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.947, 位置: [1104, 508, 1348, 711]
2025-08-04 17:12:15 | INFO     | src.state_machine.action_detector:_log_statistics:396 | 统计信息 - 帧数: 3000, 运行时间: 101.9s, FPS: 29.4, 状态: idle, 动作数: 2
2025-08-04 17:12:15 | INFO     | src.state_machine.action_detector:_log_statistics:404 | 检测统计 - 有检测的帧: 3000/3000 (100.0%), 总检测数: 9140, 手: 140, 蓝色箱子: 3000, 上层黄色箱子: 3000, 下层黄色箱子: 3000
2025-08-04 17:12:15 | INFO     | src.state_machine.action_detector:_log_statistics:414 | 性能统计 - 平均推理时间: 11.8ms, 估计FPS: 84.5, 设备: 0
2025-08-04 17:12:15 | INFO     | src.state_machine.action_detector:_log_statistics:419 | GPU内存 - 已分配: 43.5MB, 已缓存: 90.0MB
2025-08-04 17:12:19 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 3100: 检测到 3 个对象
2025-08-04 17:12:19 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:12:19 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.938, 位置: [1, 597, 490, 898]
2025-08-04 17:12:19 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:12:19 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.903, 位置: [1168, 223, 1426, 518]
2025-08-04 17:12:19 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:12:19 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.950, 位置: [1105, 508, 1348, 711]
2025-08-04 17:12:22 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 3200: 检测到 3 个对象
2025-08-04 17:12:22 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:12:22 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.932, 位置: [1, 596, 491, 898]
2025-08-04 17:12:22 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:12:22 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.893, 位置: [1168, 224, 1425, 518]
2025-08-04 17:12:22 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:12:22 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.957, 位置: [1105, 508, 1348, 711]
2025-08-04 17:12:25 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 3300: 检测到 3 个对象
2025-08-04 17:12:25 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:12:25 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.934, 位置: [1, 596, 491, 898]
2025-08-04 17:12:25 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:12:25 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.891, 位置: [1168, 223, 1425, 518]
2025-08-04 17:12:25 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:12:25 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.954, 位置: [1106, 508, 1348, 711]
2025-08-04 17:12:25 | INFO     | src.state_machine.action_detector:_log_statistics:396 | 统计信息 - 帧数: 3300, 运行时间: 112.0s, FPS: 29.5, 状态: idle, 动作数: 2
2025-08-04 17:12:25 | INFO     | src.state_machine.action_detector:_log_statistics:404 | 检测统计 - 有检测的帧: 3300/3300 (100.0%), 总检测数: 10040, 手: 140, 蓝色箱子: 3300, 上层黄色箱子: 3300, 下层黄色箱子: 3300
2025-08-04 17:12:25 | INFO     | src.state_machine.action_detector:_log_statistics:414 | 性能统计 - 平均推理时间: 11.8ms, 估计FPS: 85.1, 设备: 0
2025-08-04 17:12:25 | INFO     | src.state_machine.action_detector:_log_statistics:419 | GPU内存 - 已分配: 43.5MB, 已缓存: 90.0MB
2025-08-04 17:12:29 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 3400: 检测到 3 个对象
2025-08-04 17:12:29 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:12:29 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.929, 位置: [1, 597, 490, 898]
2025-08-04 17:12:29 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:12:29 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.888, 位置: [1168, 223, 1426, 518]
2025-08-04 17:12:29 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:12:29 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.944, 位置: [1105, 508, 1348, 712]
2025-08-04 17:12:32 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 3500: 检测到 3 个对象
2025-08-04 17:12:32 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:12:32 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.928, 位置: [1, 597, 491, 898]
2025-08-04 17:12:32 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:12:32 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.894, 位置: [1168, 223, 1426, 518]
2025-08-04 17:12:32 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:12:32 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.949, 位置: [1107, 508, 1348, 711]
2025-08-04 17:12:35 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 3600: 检测到 3 个对象
2025-08-04 17:12:35 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:12:35 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.937, 位置: [1, 596, 490, 898]
2025-08-04 17:12:35 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:12:35 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.886, 位置: [1168, 224, 1426, 518]
2025-08-04 17:12:35 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:12:35 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.951, 位置: [1107, 508, 1348, 712]
2025-08-04 17:12:35 | INFO     | src.state_machine.action_detector:_log_statistics:396 | 统计信息 - 帧数: 3600, 运行时间: 122.0s, FPS: 29.5, 状态: idle, 动作数: 2
2025-08-04 17:12:35 | INFO     | src.state_machine.action_detector:_log_statistics:404 | 检测统计 - 有检测的帧: 3600/3600 (100.0%), 总检测数: 10940, 手: 140, 蓝色箱子: 3600, 上层黄色箱子: 3600, 下层黄色箱子: 3600
2025-08-04 17:12:35 | INFO     | src.state_machine.action_detector:_log_statistics:414 | 性能统计 - 平均推理时间: 11.7ms, 估计FPS: 85.4, 设备: 0
2025-08-04 17:12:35 | INFO     | src.state_machine.action_detector:_log_statistics:419 | GPU内存 - 已分配: 43.5MB, 已缓存: 90.0MB
2025-08-04 17:12:39 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 3700: 检测到 3 个对象
2025-08-04 17:12:39 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:12:39 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.937, 位置: [0, 588, 491, 898]
2025-08-04 17:12:39 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:12:39 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.886, 位置: [1168, 223, 1425, 518]
2025-08-04 17:12:39 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:12:39 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.943, 位置: [1106, 508, 1348, 711]
2025-08-04 17:12:42 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 3800: 检测到 3 个对象
2025-08-04 17:12:42 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:12:42 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.931, 位置: [0, 586, 491, 898]
2025-08-04 17:12:42 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:12:42 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.895, 位置: [1168, 222, 1426, 518]
2025-08-04 17:12:42 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:12:42 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.921, 位置: [1104, 507, 1348, 710]
2025-08-04 17:12:45 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 3900: 检测到 3 个对象
2025-08-04 17:12:45 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:12:45 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.941, 位置: [1, 597, 490, 898]
2025-08-04 17:12:45 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:12:45 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.900, 位置: [1168, 224, 1426, 518]
2025-08-04 17:12:45 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:12:45 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.946, 位置: [1106, 508, 1348, 711]
2025-08-04 17:12:45 | INFO     | src.state_machine.action_detector:_log_statistics:396 | 统计信息 - 帧数: 3900, 运行时间: 132.0s, FPS: 29.5, 状态: idle, 动作数: 2
2025-08-04 17:12:45 | INFO     | src.state_machine.action_detector:_log_statistics:404 | 检测统计 - 有检测的帧: 3900/3900 (100.0%), 总检测数: 11840, 手: 140, 蓝色箱子: 3900, 上层黄色箱子: 3900, 下层黄色箱子: 3900
2025-08-04 17:12:45 | INFO     | src.state_machine.action_detector:_log_statistics:414 | 性能统计 - 平均推理时间: 11.7ms, 估计FPS: 85.6, 设备: 0
2025-08-04 17:12:45 | INFO     | src.state_machine.action_detector:_log_statistics:419 | GPU内存 - 已分配: 43.5MB, 已缓存: 90.0MB
2025-08-04 17:12:49 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 4000: 检测到 3 个对象
2025-08-04 17:12:49 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:12:49 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.936, 位置: [1, 596, 489, 898]
2025-08-04 17:12:49 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:12:49 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.901, 位置: [1168, 223, 1426, 518]
2025-08-04 17:12:49 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:12:49 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.938, 位置: [1107, 508, 1348, 711]
2025-08-04 17:12:49 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与blue_box交互，IoU: 0.110, 距离: 167.3
2025-08-04 17:12:49 | INFO     | src.state_machine.action_state_machine:_handle_detecting_state:130 | 手部与blue_box的交互中断
2025-08-04 17:12:49 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与blue_box交互，IoU: 0.119, 距离: 109.0
2025-08-04 17:12:49 | INFO     | src.state_machine.action_state_machine:_handle_detecting_state:130 | 手部与blue_box的交互中断
2025-08-04 17:12:49 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与blue_box交互，IoU: 0.177, 距离: 14.6
2025-08-04 17:12:50 | SUCCESS  | src.state_machine.action_state_machine:_handle_detecting_state:155 | 检测到ok_action！第3次动作，持续时间: 0.53秒
2025-08-04 17:12:50 | SUCCESS  | src.state_machine.action_detector:_handle_action_detected:198 | 检测到动作: action_1754298770_3 (类型: ok_action)
2025-08-04 17:12:50 | SUCCESS  | src.utils.video_buffer:save_action_video:117 | 动作视频已保存: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_ok/action_1754298770_3_20250804_171250.mp4 (31帧)
2025-08-04 17:12:50 | SUCCESS  | src.state_machine.action_detector:_handle_action_detected:206 | 动作视频已保存: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_ok/action_1754298770_3_20250804_171250.mp4
2025-08-04 17:12:50 | ERROR    | src.state_machine.action_detector:_save_action_image:246 | 保存动作图像失败: 'tracked_object'
2025-08-04 17:12:50 | SUCCESS  | __main__:action_callback:132 | 🎯 检测到动作 #action_1754298770_3
2025-08-04 17:12:50 | INFO     | __main__:action_callback:133 |    动作类型: ✅ OK动作 - 产品放入蓝色箱子
2025-08-04 17:12:50 | INFO     | __main__:action_callback:134 |    容器类型: blue_box
2025-08-04 17:12:50 | INFO     | __main__:action_callback:135 |    时间戳: 17:12:50
2025-08-04 17:12:50 | INFO     | __main__:action_callback:139 |    交互时长: 0.53秒
2025-08-04 17:12:50 | INFO     | __main__:action_callback:140 |    IoU: 0.150
2025-08-04 17:12:50 | INFO     | __main__:action_callback:141 |    距离: 45.1px
2025-08-04 17:12:50 | INFO     | __main__:action_callback:142 |    置信度: 0.562
2025-08-04 17:12:50 | INFO     | __main__:action_callback:145 |    保存路径: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_ok/
2025-08-04 17:12:52 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 4100: 检测到 3 个对象
2025-08-04 17:12:52 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:12:52 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.957, 位置: [3, 591, 482, 898]
2025-08-04 17:12:52 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:12:52 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.916, 位置: [1168, 223, 1426, 518]
2025-08-04 17:12:52 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:12:52 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.942, 位置: [1105, 507, 1348, 712]
2025-08-04 17:12:55 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 4200: 检测到 3 个对象
2025-08-04 17:12:55 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:12:55 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.939, 位置: [3, 588, 479, 898]
2025-08-04 17:12:55 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:12:55 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.899, 位置: [1168, 224, 1426, 518]
2025-08-04 17:12:55 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:12:55 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.946, 位置: [1107, 507, 1348, 711]
2025-08-04 17:12:55 | INFO     | src.state_machine.action_detector:_log_statistics:396 | 统计信息 - 帧数: 4200, 运行时间: 142.2s, FPS: 29.5, 状态: idle, 动作数: 3
2025-08-04 17:12:55 | INFO     | src.state_machine.action_detector:_log_statistics:404 | 检测统计 - 有检测的帧: 4200/4200 (100.0%), 总检测数: 12820, 手: 220, 蓝色箱子: 4200, 上层黄色箱子: 4200, 下层黄色箱子: 4200
2025-08-04 17:12:55 | INFO     | src.state_machine.action_detector:_log_statistics:414 | 性能统计 - 平均推理时间: 11.7ms, 估计FPS: 85.8, 设备: 0
2025-08-04 17:12:55 | INFO     | src.state_machine.action_detector:_log_statistics:419 | GPU内存 - 已分配: 43.5MB, 已缓存: 90.0MB
2025-08-04 17:12:59 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 4300: 检测到 3 个对象
2025-08-04 17:12:59 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:12:59 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.941, 位置: [3, 590, 480, 898]
2025-08-04 17:12:59 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:12:59 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.885, 位置: [1168, 224, 1426, 518]
2025-08-04 17:12:59 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:12:59 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.952, 位置: [1105, 508, 1348, 711]
2025-08-04 17:13:02 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 4400: 检测到 3 个对象
2025-08-04 17:13:02 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:13:02 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.952, 位置: [3, 589, 480, 898]
2025-08-04 17:13:02 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:13:02 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.903, 位置: [1168, 223, 1426, 518]
2025-08-04 17:13:02 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:13:02 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.931, 位置: [1106, 508, 1348, 711]
2025-08-04 17:13:05 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 4500: 检测到 3 个对象
2025-08-04 17:13:05 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:13:05 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.943, 位置: [3, 589, 478, 898]
2025-08-04 17:13:05 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:13:05 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.905, 位置: [1168, 223, 1426, 518]
2025-08-04 17:13:05 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:13:05 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.932, 位置: [1106, 507, 1348, 711]
2025-08-04 17:13:05 | INFO     | src.state_machine.action_detector:_log_statistics:396 | 统计信息 - 帧数: 4500, 运行时间: 152.2s, FPS: 29.6, 状态: idle, 动作数: 3
2025-08-04 17:13:05 | INFO     | src.state_machine.action_detector:_log_statistics:404 | 检测统计 - 有检测的帧: 4500/4500 (100.0%), 总检测数: 13720, 手: 220, 蓝色箱子: 4500, 上层黄色箱子: 4500, 下层黄色箱子: 4500
2025-08-04 17:13:05 | INFO     | src.state_machine.action_detector:_log_statistics:414 | 性能统计 - 平均推理时间: 11.6ms, 估计FPS: 85.9, 设备: 0
2025-08-04 17:13:05 | INFO     | src.state_machine.action_detector:_log_statistics:419 | GPU内存 - 已分配: 43.5MB, 已缓存: 90.0MB
2025-08-04 17:13:09 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 4600: 检测到 3 个对象
2025-08-04 17:13:09 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:13:09 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.941, 位置: [3, 589, 479, 898]
2025-08-04 17:13:09 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:13:09 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.889, 位置: [1168, 223, 1426, 518]
2025-08-04 17:13:09 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:13:09 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.946, 位置: [1106, 508, 1348, 711]
2025-08-04 17:13:11 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与blue_box交互，IoU: 0.104, 距离: 126.8
2025-08-04 17:13:11 | INFO     | src.state_machine.action_state_machine:_handle_detecting_state:130 | 手部与blue_box的交互中断
2025-08-04 17:13:11 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与blue_box交互，IoU: 0.161, 距离: 55.7
2025-08-04 17:13:11 | INFO     | src.state_machine.action_state_machine:_handle_detecting_state:130 | 手部与blue_box的交互中断
2025-08-04 17:13:12 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与blue_box交互，IoU: 0.173, 距离: 47.9
2025-08-04 17:13:12 | INFO     | src.state_machine.action_state_machine:_handle_detecting_state:130 | 手部与blue_box的交互中断
2025-08-04 17:13:12 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与blue_box交互，IoU: 0.202, 距离: 65.7
2025-08-04 17:13:12 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 4700: 检测到 4 个对象
2025-08-04 17:13:12 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - hand: 1 个
2025-08-04 17:13:12 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.516, 位置: [150, 757, 326, 898]
2025-08-04 17:13:12 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:13:12 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.713, 位置: [5, 618, 463, 898]
2025-08-04 17:13:12 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:13:12 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.909, 位置: [1168, 223, 1426, 518]
2025-08-04 17:13:12 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:13:12 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.946, 位置: [1103, 508, 1348, 711]
2025-08-04 17:13:12 | INFO     | src.state_machine.action_state_machine:_handle_detecting_state:130 | 手部与blue_box的交互中断
2025-08-04 17:13:12 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与blue_box交互，IoU: 0.155, 距离: 81.6
2025-08-04 17:13:12 | INFO     | src.state_machine.action_state_machine:_handle_detecting_state:130 | 手部与blue_box的交互中断
2025-08-04 17:13:13 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与blue_box交互，IoU: 0.122, 距离: 74.6
2025-08-04 17:13:13 | INFO     | src.state_machine.action_state_machine:_handle_detecting_state:130 | 手部与blue_box的交互中断
2025-08-04 17:13:13 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与blue_box交互，IoU: 0.234, 距离: 32.0
2025-08-04 17:13:13 | SUCCESS  | src.state_machine.action_state_machine:_handle_detecting_state:155 | 检测到ok_action！第4次动作，持续时间: 0.50秒
2025-08-04 17:13:13 | SUCCESS  | src.state_machine.action_detector:_handle_action_detected:198 | 检测到动作: action_1754298793_4 (类型: ok_action)
2025-08-04 17:13:14 | SUCCESS  | src.utils.video_buffer:save_action_video:117 | 动作视频已保存: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_ok/action_1754298793_4_20250804_171313.mp4 (31帧)
2025-08-04 17:13:14 | SUCCESS  | src.state_machine.action_detector:_handle_action_detected:206 | 动作视频已保存: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_ok/action_1754298793_4_20250804_171313.mp4
2025-08-04 17:13:14 | ERROR    | src.state_machine.action_detector:_save_action_image:246 | 保存动作图像失败: 'tracked_object'
2025-08-04 17:13:14 | SUCCESS  | __main__:action_callback:132 | 🎯 检测到动作 #action_1754298793_4
2025-08-04 17:13:14 | INFO     | __main__:action_callback:133 |    动作类型: ✅ OK动作 - 产品放入蓝色箱子
2025-08-04 17:13:14 | INFO     | __main__:action_callback:134 |    容器类型: blue_box
2025-08-04 17:13:14 | INFO     | __main__:action_callback:135 |    时间戳: 17:13:13
2025-08-04 17:13:14 | INFO     | __main__:action_callback:139 |    交互时长: 0.50秒
2025-08-04 17:13:14 | INFO     | __main__:action_callback:140 |    IoU: 0.147
2025-08-04 17:13:14 | INFO     | __main__:action_callback:141 |    距离: 55.4px
2025-08-04 17:13:14 | INFO     | __main__:action_callback:142 |    置信度: 0.517
2025-08-04 17:13:14 | INFO     | __main__:action_callback:145 |    保存路径: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_ok/
2025-08-04 17:13:16 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 4800: 检测到 3 个对象
2025-08-04 17:13:16 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:13:16 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.846, 位置: [6, 670, 437, 898]
2025-08-04 17:13:16 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:13:16 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.909, 位置: [1168, 223, 1426, 518]
2025-08-04 17:13:16 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:13:16 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.947, 位置: [1105, 508, 1348, 711]
2025-08-04 17:13:16 | INFO     | src.state_machine.action_detector:_log_statistics:396 | 统计信息 - 帧数: 4800, 运行时间: 162.4s, FPS: 29.5, 状态: idle, 动作数: 4
2025-08-04 17:13:16 | INFO     | src.state_machine.action_detector:_log_statistics:404 | 检测统计 - 有检测的帧: 4800/4800 (100.0%), 总检测数: 14702, 手: 302, 蓝色箱子: 4800, 上层黄色箱子: 4800, 下层黄色箱子: 4800
2025-08-04 17:13:16 | INFO     | src.state_machine.action_detector:_log_statistics:414 | 性能统计 - 平均推理时间: 11.6ms, 估计FPS: 86.1, 设备: 0
2025-08-04 17:13:16 | INFO     | src.state_machine.action_detector:_log_statistics:419 | GPU内存 - 已分配: 43.5MB, 已缓存: 90.0MB
2025-08-04 17:13:17 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与blue_box交互，IoU: 0.135, 距离: 40.0
2025-08-04 17:13:17 | INFO     | src.state_machine.action_state_machine:_handle_detecting_state:130 | 手部与blue_box的交互中断
2025-08-04 17:13:19 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 4900: 检测到 3 个对象
2025-08-04 17:13:19 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:13:19 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.940, 位置: [0, 597, 491, 898]
2025-08-04 17:13:19 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:13:19 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.898, 位置: [1168, 223, 1426, 518]
2025-08-04 17:13:19 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:13:19 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.943, 位置: [1106, 507, 1348, 711]
2025-08-04 17:13:22 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 5000: 检测到 3 个对象
2025-08-04 17:13:22 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:13:22 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.940, 位置: [0, 597, 490, 898]
2025-08-04 17:13:22 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:13:22 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.912, 位置: [1168, 223, 1426, 518]
2025-08-04 17:13:22 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:13:22 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.935, 位置: [1106, 507, 1348, 711]
2025-08-04 17:13:26 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 5100: 检测到 4 个对象
2025-08-04 17:13:26 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - hand: 1 个
2025-08-04 17:13:26 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.589, 位置: [327, 585, 468, 679]
2025-08-04 17:13:26 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:13:26 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.942, 位置: [4, 585, 471, 898]
2025-08-04 17:13:26 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:13:26 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.907, 位置: [1168, 223, 1426, 518]
2025-08-04 17:13:26 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:13:26 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.946, 位置: [1105, 507, 1348, 712]
2025-08-04 17:13:26 | INFO     | src.state_machine.action_detector:_log_statistics:396 | 统计信息 - 帧数: 5100, 运行时间: 172.5s, FPS: 29.6, 状态: idle, 动作数: 4
2025-08-04 17:13:26 | INFO     | src.state_machine.action_detector:_log_statistics:404 | 检测统计 - 有检测的帧: 5100/5100 (100.0%), 总检测数: 15607, 手: 307, 蓝色箱子: 5100, 上层黄色箱子: 5100, 下层黄色箱子: 5100
2025-08-04 17:13:26 | INFO     | src.state_machine.action_detector:_log_statistics:414 | 性能统计 - 平均推理时间: 11.6ms, 估计FPS: 86.3, 设备: 0
2025-08-04 17:13:26 | INFO     | src.state_machine.action_detector:_log_statistics:419 | GPU内存 - 已分配: 43.5MB, 已缓存: 90.0MB
2025-08-04 17:13:26 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与blue_box交互，IoU: 0.102, 距离: 80.9
2025-08-04 17:13:26 | INFO     | src.state_machine.action_state_machine:_handle_detecting_state:130 | 手部与blue_box的交互中断
2025-08-04 17:13:29 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 5200: 检测到 3 个对象
2025-08-04 17:13:29 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:13:29 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.947, 位置: [3, 590, 478, 898]
2025-08-04 17:13:29 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:13:29 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.896, 位置: [1168, 223, 1426, 518]
2025-08-04 17:13:29 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:13:29 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.951, 位置: [1106, 508, 1348, 712]
2025-08-04 17:13:32 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 5300: 检测到 3 个对象
2025-08-04 17:13:32 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:13:32 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.943, 位置: [3, 582, 480, 898]
2025-08-04 17:13:32 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:13:32 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.904, 位置: [1168, 223, 1426, 518]
2025-08-04 17:13:32 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:13:32 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.940, 位置: [1106, 508, 1348, 711]
2025-08-04 17:13:36 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 5400: 检测到 3 个对象
2025-08-04 17:13:36 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:13:36 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.955, 位置: [3, 582, 479, 898]
2025-08-04 17:13:36 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:13:36 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.900, 位置: [1168, 224, 1426, 518]
2025-08-04 17:13:36 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:13:36 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.941, 位置: [1107, 508, 1348, 711]
2025-08-04 17:13:36 | INFO     | src.state_machine.action_detector:_log_statistics:396 | 统计信息 - 帧数: 5400, 运行时间: 182.5s, FPS: 29.6, 状态: idle, 动作数: 4
2025-08-04 17:13:36 | INFO     | src.state_machine.action_detector:_log_statistics:404 | 检测统计 - 有检测的帧: 5400/5400 (100.0%), 总检测数: 16513, 手: 313, 蓝色箱子: 5400, 上层黄色箱子: 5400, 下层黄色箱子: 5400
2025-08-04 17:13:36 | INFO     | src.state_machine.action_detector:_log_statistics:414 | 性能统计 - 平均推理时间: 11.6ms, 估计FPS: 86.5, 设备: 0
2025-08-04 17:13:36 | INFO     | src.state_machine.action_detector:_log_statistics:419 | GPU内存 - 已分配: 43.5MB, 已缓存: 90.0MB
2025-08-04 17:13:39 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 5500: 检测到 3 个对象
2025-08-04 17:13:39 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:13:39 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.941, 位置: [1, 587, 492, 898]
2025-08-04 17:13:39 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:13:39 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.907, 位置: [1168, 223, 1426, 518]
2025-08-04 17:13:39 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:13:39 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.946, 位置: [1106, 507, 1348, 711]
2025-08-04 17:13:42 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 5600: 检测到 3 个对象
2025-08-04 17:13:42 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:13:42 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.954, 位置: [0, 586, 491, 898]
2025-08-04 17:13:42 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:13:42 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.898, 位置: [1168, 223, 1426, 518]
2025-08-04 17:13:42 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:13:42 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.930, 位置: [1107, 508, 1348, 711]
2025-08-04 17:13:46 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 5700: 检测到 3 个对象
2025-08-04 17:13:46 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:13:46 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.959, 位置: [3, 582, 483, 898]
2025-08-04 17:13:46 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:13:46 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.905, 位置: [1168, 223, 1426, 518]
2025-08-04 17:13:46 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:13:46 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.935, 位置: [1106, 507, 1348, 711]
2025-08-04 17:13:46 | INFO     | src.state_machine.action_detector:_log_statistics:396 | 统计信息 - 帧数: 5700, 运行时间: 192.5s, FPS: 29.6, 状态: idle, 动作数: 4
2025-08-04 17:13:46 | INFO     | src.state_machine.action_detector:_log_statistics:404 | 检测统计 - 有检测的帧: 5700/5700 (100.0%), 总检测数: 17422, 手: 322, 蓝色箱子: 5700, 上层黄色箱子: 5700, 下层黄色箱子: 5700
2025-08-04 17:13:46 | INFO     | src.state_machine.action_detector:_log_statistics:414 | 性能统计 - 平均推理时间: 11.5ms, 估计FPS: 87.0, 设备: 0
2025-08-04 17:13:46 | INFO     | src.state_machine.action_detector:_log_statistics:419 | GPU内存 - 已分配: 43.5MB, 已缓存: 90.0MB
2025-08-04 17:13:49 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 5800: 检测到 3 个对象
2025-08-04 17:13:49 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:13:49 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.949, 位置: [1, 585, 491, 898]
2025-08-04 17:13:49 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:13:49 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.900, 位置: [1169, 223, 1426, 518]
2025-08-04 17:13:49 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:13:49 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.945, 位置: [1104, 507, 1348, 711]
2025-08-04 17:13:52 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与blue_box交互，IoU: 0.158, 距离: 52.8
2025-08-04 17:13:52 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 5900: 检测到 4 个对象
2025-08-04 17:13:52 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - hand: 1 个
2025-08-04 17:13:52 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.553, 位置: [165, 799, 306, 897]
2025-08-04 17:13:52 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:13:52 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.751, 位置: [6, 658, 435, 898]
2025-08-04 17:13:52 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:13:52 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.913, 位置: [1168, 223, 1426, 518]
2025-08-04 17:13:52 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:13:52 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.943, 位置: [1104, 507, 1348, 711]
2025-08-04 17:13:53 | INFO     | src.state_machine.action_state_machine:_handle_detecting_state:130 | 手部与blue_box的交互中断
2025-08-04 17:13:54 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与blue_box交互，IoU: 0.119, 距离: 69.6
2025-08-04 17:13:54 | INFO     | src.state_machine.action_state_machine:_handle_detecting_state:130 | 手部与blue_box的交互中断
2025-08-04 17:13:54 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与blue_box交互，IoU: 0.120, 距离: 34.2
2025-08-04 17:13:54 | INFO     | src.state_machine.action_state_machine:_handle_detecting_state:130 | 手部与blue_box的交互中断
2025-08-04 17:13:54 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与blue_box交互，IoU: 0.144, 距离: 70.5
2025-08-04 17:13:54 | INFO     | src.state_machine.action_state_machine:_handle_detecting_state:130 | 手部与blue_box的交互中断
2025-08-04 17:13:56 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与blue_box交互，IoU: 0.236, 距离: 44.4
2025-08-04 17:13:56 | INFO     | src.state_machine.action_state_machine:_handle_detecting_state:130 | 手部与blue_box的交互中断
2025-08-04 17:13:56 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 6000: 检测到 3 个对象
2025-08-04 17:13:56 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:13:56 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.803, 位置: [5, 670, 439, 898]
2025-08-04 17:13:56 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:13:56 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.902, 位置: [1168, 224, 1426, 518]
2025-08-04 17:13:56 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:13:56 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.951, 位置: [1105, 507, 1348, 711]
2025-08-04 17:13:56 | INFO     | src.state_machine.action_detector:_log_statistics:396 | 统计信息 - 帧数: 6000, 运行时间: 202.6s, FPS: 29.6, 状态: idle, 动作数: 4
2025-08-04 17:13:56 | INFO     | src.state_machine.action_detector:_log_statistics:404 | 检测统计 - 有检测的帧: 6000/6000 (100.0%), 总检测数: 18349, 手: 349, 蓝色箱子: 6000, 上层黄色箱子: 6000, 下层黄色箱子: 6000
2025-08-04 17:13:56 | INFO     | src.state_machine.action_detector:_log_statistics:414 | 性能统计 - 平均推理时间: 11.5ms, 估计FPS: 86.8, 设备: 0
2025-08-04 17:13:56 | INFO     | src.state_machine.action_detector:_log_statistics:419 | GPU内存 - 已分配: 43.5MB, 已缓存: 90.0MB
2025-08-04 17:13:56 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与blue_box交互，IoU: 0.101, 距离: 73.3
2025-08-04 17:13:56 | INFO     | src.state_machine.action_state_machine:_handle_detecting_state:130 | 手部与blue_box的交互中断
2025-08-04 17:13:56 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与blue_box交互，IoU: 0.101, 距离: 65.6
2025-08-04 17:13:57 | INFO     | src.state_machine.action_state_machine:_handle_detecting_state:130 | 手部与blue_box的交互中断
2025-08-04 17:13:57 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与blue_box交互，IoU: 0.108, 距离: 62.3
2025-08-04 17:13:57 | INFO     | src.state_machine.action_state_machine:_handle_detecting_state:130 | 手部与blue_box的交互中断
2025-08-04 17:13:57 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与blue_box交互，IoU: 0.107, 距离: 66.2
2025-08-04 17:13:57 | INFO     | src.state_machine.action_state_machine:_handle_detecting_state:130 | 手部与blue_box的交互中断
2025-08-04 17:13:57 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与blue_box交互，IoU: 0.114, 距离: 72.8
2025-08-04 17:13:58 | SUCCESS  | src.state_machine.action_state_machine:_handle_detecting_state:155 | 检测到ok_action！第5次动作，持续时间: 0.54秒
2025-08-04 17:13:58 | SUCCESS  | src.state_machine.action_detector:_handle_action_detected:198 | 检测到动作: action_1754298838_5 (类型: ok_action)
2025-08-04 17:13:58 | SUCCESS  | src.utils.video_buffer:save_action_video:117 | 动作视频已保存: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_ok/action_1754298838_5_20250804_171358.mp4 (31帧)
2025-08-04 17:13:58 | SUCCESS  | src.state_machine.action_detector:_handle_action_detected:206 | 动作视频已保存: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_ok/action_1754298838_5_20250804_171358.mp4
2025-08-04 17:13:58 | ERROR    | src.state_machine.action_detector:_save_action_image:246 | 保存动作图像失败: 'tracked_object'
2025-08-04 17:13:58 | SUCCESS  | __main__:action_callback:132 | 🎯 检测到动作 #action_1754298838_5
2025-08-04 17:13:58 | INFO     | __main__:action_callback:133 |    动作类型: ✅ OK动作 - 产品放入蓝色箱子
2025-08-04 17:13:58 | INFO     | __main__:action_callback:134 |    容器类型: blue_box
2025-08-04 17:13:58 | INFO     | __main__:action_callback:135 |    时间戳: 17:13:58
2025-08-04 17:13:58 | INFO     | __main__:action_callback:139 |    交互时长: 0.54秒
2025-08-04 17:13:58 | INFO     | __main__:action_callback:140 |    IoU: 0.159
2025-08-04 17:13:58 | INFO     | __main__:action_callback:141 |    距离: 45.0px
2025-08-04 17:13:58 | INFO     | __main__:action_callback:142 |    置信度: 0.518
2025-08-04 17:13:58 | INFO     | __main__:action_callback:145 |    保存路径: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_ok/
2025-08-04 17:13:59 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 6100: 检测到 3 个对象
2025-08-04 17:13:59 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:13:59 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.951, 位置: [1, 587, 493, 898]
2025-08-04 17:13:59 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:13:59 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.906, 位置: [1168, 223, 1426, 518]
2025-08-04 17:13:59 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:13:59 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.942, 位置: [1104, 507, 1348, 711]
2025-08-04 17:14:03 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 6200: 检测到 3 个对象
2025-08-04 17:14:03 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:14:03 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.950, 位置: [1, 587, 490, 898]
2025-08-04 17:14:03 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:14:03 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.900, 位置: [1168, 223, 1426, 518]
2025-08-04 17:14:03 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:14:03 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.949, 位置: [1106, 508, 1348, 711]
2025-08-04 17:14:06 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 6300: 检测到 3 个对象
2025-08-04 17:14:06 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:14:06 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.952, 位置: [1, 587, 490, 898]
2025-08-04 17:14:06 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:14:06 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.905, 位置: [1168, 223, 1426, 518]
2025-08-04 17:14:06 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:14:06 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.939, 位置: [1106, 507, 1348, 712]
2025-08-04 17:14:06 | INFO     | src.state_machine.action_detector:_log_statistics:396 | 统计信息 - 帧数: 6300, 运行时间: 212.8s, FPS: 29.6, 状态: idle, 动作数: 5
2025-08-04 17:14:06 | INFO     | src.state_machine.action_detector:_log_statistics:404 | 检测统计 - 有检测的帧: 6300/6300 (100.0%), 总检测数: 19296, 手: 396, 蓝色箱子: 6300, 上层黄色箱子: 6300, 下层黄色箱子: 6300
2025-08-04 17:14:06 | INFO     | src.state_machine.action_detector:_log_statistics:414 | 性能统计 - 平均推理时间: 11.6ms, 估计FPS: 86.4, 设备: 0
2025-08-04 17:14:06 | INFO     | src.state_machine.action_detector:_log_statistics:419 | GPU内存 - 已分配: 43.5MB, 已缓存: 90.0MB
2025-08-04 17:14:07 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与blue_box交互，IoU: 0.124, 距离: 148.9
2025-08-04 17:14:07 | INFO     | src.state_machine.action_state_machine:_handle_detecting_state:130 | 手部与blue_box的交互中断
2025-08-04 17:14:09 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 6400: 检测到 3 个对象
2025-08-04 17:14:09 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:14:09 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.945, 位置: [1, 583, 492, 898]
2025-08-04 17:14:09 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:14:09 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.895, 位置: [1168, 223, 1426, 518]
2025-08-04 17:14:09 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:14:09 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.947, 位置: [1106, 508, 1348, 711]
2025-08-04 17:14:13 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 6500: 检测到 3 个对象
2025-08-04 17:14:13 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:14:13 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.944, 位置: [0, 584, 491, 898]
2025-08-04 17:14:13 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:14:13 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.902, 位置: [1169, 223, 1425, 518]
2025-08-04 17:14:13 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:14:13 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.948, 位置: [1105, 507, 1348, 711]
2025-08-04 17:14:16 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 6600: 检测到 3 个对象
2025-08-04 17:14:16 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:14:16 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.946, 位置: [0, 582, 492, 898]
2025-08-04 17:14:16 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:14:16 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.898, 位置: [1168, 223, 1426, 518]
2025-08-04 17:14:16 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:14:16 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.933, 位置: [1105, 508, 1348, 710]
2025-08-04 17:14:16 | INFO     | src.state_machine.action_detector:_log_statistics:396 | 统计信息 - 帧数: 6600, 运行时间: 222.8s, FPS: 29.6, 状态: idle, 动作数: 5
2025-08-04 17:14:16 | INFO     | src.state_machine.action_detector:_log_statistics:404 | 检测统计 - 有检测的帧: 6600/6600 (100.0%), 总检测数: 20213, 手: 413, 蓝色箱子: 6600, 上层黄色箱子: 6600, 下层黄色箱子: 6600
2025-08-04 17:14:16 | INFO     | src.state_machine.action_detector:_log_statistics:414 | 性能统计 - 平均推理时间: 11.6ms, 估计FPS: 85.9, 设备: 0
2025-08-04 17:14:16 | INFO     | src.state_machine.action_detector:_log_statistics:419 | GPU内存 - 已分配: 43.5MB, 已缓存: 90.0MB
2025-08-04 17:14:17 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与blue_box交互，IoU: 0.110, 距离: 129.3
2025-08-04 17:14:17 | INFO     | src.state_machine.action_state_machine:_handle_detecting_state:130 | 手部与blue_box的交互中断
2025-08-04 17:14:17 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与blue_box交互，IoU: 0.100, 距离: 82.7
2025-08-04 17:14:17 | INFO     | src.state_machine.action_state_machine:_handle_detecting_state:130 | 手部与blue_box的交互中断
2025-08-04 17:14:17 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与blue_box交互，IoU: 0.102, 距离: 72.6
2025-08-04 17:14:18 | INFO     | src.state_machine.action_state_machine:_handle_detecting_state:130 | 手部与blue_box的交互中断
2025-08-04 17:14:19 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 6700: 检测到 3 个对象
2025-08-04 17:14:19 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:14:19 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.948, 位置: [2, 584, 494, 898]
2025-08-04 17:14:19 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:14:19 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.905, 位置: [1168, 223, 1426, 518]
2025-08-04 17:14:19 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:14:19 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.943, 位置: [1105, 507, 1348, 711]
2025-08-04 17:14:23 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 6800: 检测到 3 个对象
2025-08-04 17:14:23 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:14:23 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.946, 位置: [0, 582, 491, 898]
2025-08-04 17:14:23 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:14:23 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.896, 位置: [1168, 223, 1426, 518]
2025-08-04 17:14:23 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:14:23 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.943, 位置: [1105, 507, 1348, 711]
2025-08-04 17:14:26 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 6900: 检测到 3 个对象
2025-08-04 17:14:26 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:14:26 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.947, 位置: [0, 583, 492, 898]
2025-08-04 17:14:26 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:14:26 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.904, 位置: [1168, 223, 1426, 518]
2025-08-04 17:14:26 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:14:26 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.933, 位置: [1105, 508, 1348, 711]
2025-08-04 17:14:26 | INFO     | src.state_machine.action_detector:_log_statistics:396 | 统计信息 - 帧数: 6900, 运行时间: 232.8s, FPS: 29.6, 状态: idle, 动作数: 5
2025-08-04 17:14:26 | INFO     | src.state_machine.action_detector:_log_statistics:404 | 检测统计 - 有检测的帧: 6900/6900 (100.0%), 总检测数: 21132, 手: 432, 蓝色箱子: 6900, 上层黄色箱子: 6900, 下层黄色箱子: 6900
2025-08-04 17:14:26 | INFO     | src.state_machine.action_detector:_log_statistics:414 | 性能统计 - 平均推理时间: 11.7ms, 估计FPS: 85.7, 设备: 0
2025-08-04 17:14:26 | INFO     | src.state_machine.action_detector:_log_statistics:419 | GPU内存 - 已分配: 43.5MB, 已缓存: 90.0MB
2025-08-04 17:14:27 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与blue_box交互，IoU: 0.105, 距离: 169.2
2025-08-04 17:14:27 | INFO     | src.state_machine.action_state_machine:_handle_detecting_state:130 | 手部与blue_box的交互中断
2025-08-04 17:14:28 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与blue_box交互，IoU: 0.111, 距离: 78.3
2025-08-04 17:14:28 | INFO     | src.state_machine.action_state_machine:_handle_detecting_state:130 | 手部与blue_box的交互中断
2025-08-04 17:14:28 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与blue_box交互，IoU: 0.103, 距离: 86.3
2025-08-04 17:14:28 | INFO     | src.state_machine.action_state_machine:_handle_detecting_state:130 | 手部与blue_box的交互中断
2025-08-04 17:14:28 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与blue_box交互，IoU: 0.101, 距离: 78.8
2025-08-04 17:14:28 | INFO     | src.state_machine.action_state_machine:_handle_detecting_state:130 | 手部与blue_box的交互中断
2025-08-04 17:14:29 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 7000: 检测到 3 个对象
2025-08-04 17:14:29 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:14:29 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.946, 位置: [0, 584, 492, 898]
2025-08-04 17:14:29 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:14:29 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.896, 位置: [1168, 224, 1426, 518]
2025-08-04 17:14:29 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:14:29 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.937, 位置: [1107, 508, 1348, 711]
2025-08-04 17:14:33 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 7100: 检测到 3 个对象
2025-08-04 17:14:33 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:14:33 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.947, 位置: [0, 583, 491, 898]
2025-08-04 17:14:33 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:14:33 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.901, 位置: [1168, 223, 1426, 518]
2025-08-04 17:14:33 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:14:33 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.924, 位置: [1106, 507, 1348, 711]
2025-08-04 17:14:35 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与blue_box交互，IoU: 0.141, 距离: 104.7
2025-08-04 17:14:35 | INFO     | src.state_machine.action_state_machine:_handle_detecting_state:130 | 手部与blue_box的交互中断
2025-08-04 17:14:35 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与blue_box交互，IoU: 0.164, 距离: 65.6
2025-08-04 17:14:35 | INFO     | src.state_machine.action_state_machine:_handle_detecting_state:130 | 手部与blue_box的交互中断
2025-08-04 17:14:36 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 7200: 检测到 3 个对象
2025-08-04 17:14:36 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:14:36 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.945, 位置: [0, 583, 492, 898]
2025-08-04 17:14:36 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:14:36 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.908, 位置: [1168, 223, 1426, 518]
2025-08-04 17:14:36 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:14:36 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.931, 位置: [1107, 508, 1348, 711]
2025-08-04 17:14:36 | INFO     | src.state_machine.action_detector:_log_statistics:396 | 统计信息 - 帧数: 7200, 运行时间: 242.9s, FPS: 29.6, 状态: idle, 动作数: 5
2025-08-04 17:14:36 | INFO     | src.state_machine.action_detector:_log_statistics:404 | 检测统计 - 有检测的帧: 7200/7200 (100.0%), 总检测数: 22080, 手: 480, 蓝色箱子: 7200, 上层黄色箱子: 7200, 下层黄色箱子: 7200
2025-08-04 17:14:36 | INFO     | src.state_machine.action_detector:_log_statistics:414 | 性能统计 - 平均推理时间: 11.7ms, 估计FPS: 85.4, 设备: 0
2025-08-04 17:14:36 | INFO     | src.state_machine.action_detector:_log_statistics:419 | GPU内存 - 已分配: 43.5MB, 已缓存: 90.0MB
2025-08-04 17:14:39 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 7300: 检测到 3 个对象
2025-08-04 17:14:39 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:14:39 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.941, 位置: [0, 582, 492, 898]
2025-08-04 17:14:39 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:14:39 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.897, 位置: [1169, 223, 1426, 518]
2025-08-04 17:14:39 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:14:39 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.938, 位置: [1104, 507, 1348, 711]
2025-08-04 17:14:40 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与blue_box交互，IoU: 0.114, 距离: 140.0
2025-08-04 17:14:41 | SUCCESS  | src.state_machine.action_state_machine:_handle_detecting_state:155 | 检测到ok_action！第6次动作，持续时间: 0.51秒
2025-08-04 17:14:41 | SUCCESS  | src.state_machine.action_detector:_handle_action_detected:198 | 检测到动作: action_1754298881_6 (类型: ok_action)
2025-08-04 17:14:41 | SUCCESS  | src.utils.video_buffer:save_action_video:117 | 动作视频已保存: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_ok/action_1754298881_6_20250804_171441.mp4 (31帧)
2025-08-04 17:14:41 | SUCCESS  | src.state_machine.action_detector:_handle_action_detected:206 | 动作视频已保存: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_ok/action_1754298881_6_20250804_171441.mp4
2025-08-04 17:14:41 | ERROR    | src.state_machine.action_detector:_save_action_image:246 | 保存动作图像失败: 'tracked_object'
2025-08-04 17:14:41 | SUCCESS  | __main__:action_callback:132 | 🎯 检测到动作 #action_1754298881_6
2025-08-04 17:14:41 | INFO     | __main__:action_callback:133 |    动作类型: ✅ OK动作 - 产品放入蓝色箱子
2025-08-04 17:14:41 | INFO     | __main__:action_callback:134 |    容器类型: blue_box
2025-08-04 17:14:41 | INFO     | __main__:action_callback:135 |    时间戳: 17:14:41
2025-08-04 17:14:41 | INFO     | __main__:action_callback:139 |    交互时长: 0.51秒
2025-08-04 17:14:41 | INFO     | __main__:action_callback:140 |    IoU: 0.133
2025-08-04 17:14:41 | INFO     | __main__:action_callback:141 |    距离: 21.0px
2025-08-04 17:14:41 | INFO     | __main__:action_callback:142 |    置信度: 0.626
2025-08-04 17:14:41 | INFO     | __main__:action_callback:145 |    保存路径: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_ok/
2025-08-04 17:14:43 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 7400: 检测到 3 个对象
2025-08-04 17:14:43 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:14:43 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.939, 位置: [1, 585, 493, 898]
2025-08-04 17:14:43 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:14:43 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.904, 位置: [1168, 223, 1426, 518]
2025-08-04 17:14:43 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:14:43 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.952, 位置: [1106, 507, 1348, 711]
2025-08-04 17:14:46 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 7500: 检测到 3 个对象
2025-08-04 17:14:46 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:14:46 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.930, 位置: [1, 583, 491, 898]
2025-08-04 17:14:46 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:14:46 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.902, 位置: [1168, 223, 1426, 518]
2025-08-04 17:14:46 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:14:46 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.942, 位置: [1105, 508, 1348, 711]
2025-08-04 17:14:46 | INFO     | src.state_machine.action_detector:_log_statistics:396 | 统计信息 - 帧数: 7500, 运行时间: 253.1s, FPS: 29.6, 状态: idle, 动作数: 6
2025-08-04 17:14:46 | INFO     | src.state_machine.action_detector:_log_statistics:404 | 检测统计 - 有检测的帧: 7500/7500 (100.0%), 总检测数: 23004, 手: 504, 蓝色箱子: 7500, 上层黄色箱子: 7500, 下层黄色箱子: 7500
2025-08-04 17:14:46 | INFO     | src.state_machine.action_detector:_log_statistics:414 | 性能统计 - 平均推理时间: 11.7ms, 估计FPS: 85.4, 设备: 0
2025-08-04 17:14:46 | INFO     | src.state_machine.action_detector:_log_statistics:419 | GPU内存 - 已分配: 43.5MB, 已缓存: 90.0MB
2025-08-04 17:14:50 | INFO     | src.state_machine.action_detector:_log_detection_results:373 | 帧 7600: 检测到 3 个对象
2025-08-04 17:14:50 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - blue_box: 1 个
2025-08-04 17:14:50 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.934, 位置: [1, 583, 491, 898]
2025-08-04 17:14:50 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - upper_yellow_box: 1 个
2025-08-04 17:14:50 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.887, 位置: [1168, 223, 1426, 518]
2025-08-04 17:14:50 | INFO     | src.state_machine.action_detector:_log_detection_results:376 |   - lower_yellow_box: 1 个
2025-08-04 17:14:50 | INFO     | src.state_machine.action_detector:_log_detection_results:380 |     [1] 置信度: 0.911, 位置: [1104, 509, 1348, 710]
2025-08-04 17:14:50 | INFO     | src.detection.video_capture:get_frame_generator:127 | 视频文件读取完毕
2025-08-04 17:14:50 | INFO     | src.detection.video_capture:release:203 | 视频捕获资源已释放
2025-08-04 17:14:50 | INFO     | src.state_machine.action_detector:_cleanup:434 | 检测完成 - 总帧数: 7600, 总运行时间: 256.4s, 平均FPS: 29.6, 检测到的动作数: 6
2025-08-04 17:14:50 | INFO     | __main__:main:261 | ============================================================
2025-08-04 17:14:50 | INFO     | __main__:main:262 | 👋 程序结束
2025-08-04 17:14:50 | INFO     | __main__:main:263 | ============================================================
2025-08-04 17:16:09 | INFO     | src.utils.logger:setup_logger:55 | 日志系统初始化完成，级别: INFO
2025-08-04 17:16:09 | INFO     | src.utils.logger:setup_logger:57 | 日志文件: logs/action_detection.log
2025-08-04 17:16:09 | INFO     | __main__:main:170 | ============================================================
2025-08-04 17:16:09 | INFO     | __main__:main:171 | 🚀 YOLOv8实时动作检测系统启动
2025-08-04 17:16:09 | INFO     | __main__:main:172 | ============================================================
2025-08-04 17:16:09 | INFO     | __main__:main:178 | 使用命令行指定的视频文件: /home/<USER>/wolong_ws/yolo_detect_ws/data/test_videos/test_video_long.mp4
2025-08-04 17:16:09 | INFO     | __main__:main:189 | 📹 视频源: /home/<USER>/wolong_ws/yolo_detect_ws/data/test_videos/test_video_long.mp4
2025-08-04 17:16:09 | INFO     | __main__:main:190 | 🤖 模型路径: yolo_training/yolov8n_custom2/weights/best.pt
2025-08-04 17:16:09 | INFO     | __main__:main:191 | 📁 输出目录: output/detected_actions
2025-08-04 17:16:09 | INFO     | __main__:main:192 | 🎯 置信度阈值: 0.5
2025-08-04 17:16:09 | INFO     | __main__:main:193 | 📊 交互阈值 - IoU: 0.1, 距离: 50px
2025-08-04 17:16:09 | INFO     | __main__:main:194 | ⏰ 冷却时间: 3.0秒, 动作持续时间: 0.5秒
2025-08-04 17:16:09 | INFO     | src.detection.detector:_get_device:55 | torch.cuda.is_available(): True
2025-08-04 17:16:09 | INFO     | src.detection.detector:_get_device:56 | torch.cuda.device_count(): 1
2025-08-04 17:16:09 | INFO     | src.detection.detector:_get_device:62 | 检测到GPU: NVIDIA GeForce RTX 4060 Laptop GPU (7.6GB)
2025-08-04 17:16:09 | INFO     | src.detection.detector:_get_device:66 | 使用GPU设备: cuda:0
2025-08-04 17:16:09 | INFO     | src.detection.detector:_get_device:88 | 使用设备: 0
2025-08-04 17:16:09 | INFO     | src.detection.detector:_get_device:97 | GPU缓存已清理
2025-08-04 17:16:09 | INFO     | src.detection.detector:_get_device:101 | GPU内存分配策略已设置
2025-08-04 17:16:09 | INFO     | src.detection.detector:_load_model:117 | 加载模型: yolo_training/yolov8n_custom2/weights/best.pt
2025-08-04 17:16:09 | INFO     | src.detection.detector:_load_model:123 | 将模型移动到GPU设备: 0
2025-08-04 17:16:09 | INFO     | src.detection.detector:_load_model:129 | 模型已成功移动到GPU
2025-08-04 17:16:09 | INFO     | src.detection.detector:_load_model:138 | 检测类别: {0: 'blue_box', 1: 'upper_yellow_box', 2: 'lower_yellow_box', 3: 'hand'}
2025-08-04 17:16:09 | INFO     | src.detection.video_capture:_initialize_capture:52 | 初始化视频文件: /home/<USER>/wolong_ws/yolo_detect_ws/data/test_videos/test_video_long.mp4
2025-08-04 17:16:09 | INFO     | src.detection.video_capture:_log_video_info:74 | 视频信息 - 分辨率: 1442x898, FPS: 25.00
2025-08-04 17:16:09 | INFO     | src.detection.video_capture:_log_video_info:79 | 视频时长: 304.00秒, 总帧数: 7600
2025-08-04 17:16:09 | INFO     | src.state_machine.action_state_machine:__init__:73 | 状态机初始化完成，初始状态: idle
2025-08-04 17:16:09 | INFO     | src.utils.video_buffer:__init__:33 | 视频缓存器初始化完成，缓存大小: 31帧，帧率: 30.0fps
2025-08-04 17:16:09 | INFO     | src.utils.video_buffer:__init__:168 | 确保目录存在: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_ok
2025-08-04 17:16:09 | INFO     | src.utils.video_buffer:__init__:168 | 确保目录存在: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_nok_electric
2025-08-04 17:16:09 | INFO     | src.utils.video_buffer:__init__:168 | 确保目录存在: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_nok_appearence
2025-08-04 17:16:09 | INFO     | src.state_machine.action_detector:__init__:96 | 视频保存功能已启用，缓存大小: 31帧
2025-08-04 17:16:09 | INFO     | src.state_machine.action_detector:__init__:120 | 动作检测器初始化完成
2025-08-04 17:16:09 | INFO     | __main__:main:226 | 🖥️  推理设备: 0
2025-08-04 17:16:09 | INFO     | __main__:main:237 | 💡 控制提示:
2025-08-04 17:16:09 | INFO     | __main__:main:238 |    按 'q' 或 ESC 退出程序
2025-08-04 17:16:09 | INFO     | __main__:main:239 |    按 'r' 重置状态机
2025-08-04 17:16:09 | INFO     | __main__:main:240 |    按 'd' 切换调试模式
2025-08-04 17:16:09 | INFO     | __main__:main:242 | 🔍 开始检测...
2025-08-04 17:16:09 | INFO     | __main__:main:243 | ------------------------------------------------------------
2025-08-04 17:16:09 | INFO     | src.state_machine.action_detector:run:139 | 开始运行动作检测
2025-08-04 17:16:10 | INFO     | src.detection.detector:detect:223 | 检测调试 #1: 原始检测数=3, 过滤后检测数=3, 置信度阈值=0.5
2025-08-04 17:16:10 | INFO     | src.detection.detector:detect:225 | 推理时间: 1102.7ms, 平均: 1102.7ms
2025-08-04 17:16:10 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 17:16:10 | INFO     | src.detection.detector:detect:233 | 原始类别: [2.0, 0.0, 1.0]
2025-08-04 17:16:10 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.9483245611190796, 0.9287644624710083, 0.9003223180770874]
2025-08-04 17:16:10 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 1: 检测到 3 个对象
2025-08-04 17:16:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:16:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.929, 位置: [0, 586, 491, 898]
2025-08-04 17:16:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:16:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.900, 位置: [1168, 223, 1426, 518]
2025-08-04 17:16:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:16:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.948, 位置: [1105, 507, 1348, 711]
2025-08-04 17:16:10 | INFO     | src.detection.detector:detect:223 | 检测调试 #2: 原始检测数=3, 过滤后检测数=3, 置信度阈值=0.5
2025-08-04 17:16:10 | INFO     | src.detection.detector:detect:225 | 推理时间: 7.4ms, 平均: 555.1ms
2025-08-04 17:16:10 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 17:16:10 | INFO     | src.detection.detector:detect:233 | 原始类别: [2.0, 0.0, 1.0]
2025-08-04 17:16:10 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.9426160454750061, 0.92695152759552, 0.9040740132331848]
2025-08-04 17:16:10 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 2: 检测到 3 个对象
2025-08-04 17:16:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:16:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.927, 位置: [1, 596, 490, 898]
2025-08-04 17:16:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:16:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.904, 位置: [1169, 223, 1426, 518]
2025-08-04 17:16:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:16:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.943, 位置: [1104, 507, 1348, 711]
2025-08-04 17:16:10 | INFO     | src.detection.detector:detect:223 | 检测调试 #3: 原始检测数=3, 过滤后检测数=3, 置信度阈值=0.5
2025-08-04 17:16:10 | INFO     | src.detection.detector:detect:225 | 推理时间: 7.3ms, 平均: 372.5ms
2025-08-04 17:16:10 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 17:16:10 | INFO     | src.detection.detector:detect:233 | 原始类别: [2.0, 0.0, 1.0]
2025-08-04 17:16:10 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.9422558546066284, 0.9203870296478271, 0.9028117656707764]
2025-08-04 17:16:10 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 3: 检测到 3 个对象
2025-08-04 17:16:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:16:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.920, 位置: [1, 596, 490, 898]
2025-08-04 17:16:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:16:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.903, 位置: [1169, 223, 1426, 518]
2025-08-04 17:16:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:16:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.942, 位置: [1104, 507, 1348, 711]
2025-08-04 17:16:10 | INFO     | src.detection.detector:detect:223 | 检测调试 #4: 原始检测数=3, 过滤后检测数=3, 置信度阈值=0.5
2025-08-04 17:16:10 | INFO     | src.detection.detector:detect:225 | 推理时间: 6.7ms, 平均: 281.0ms
2025-08-04 17:16:10 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 17:16:10 | INFO     | src.detection.detector:detect:233 | 原始类别: [2.0, 0.0, 1.0]
2025-08-04 17:16:10 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.9385360479354858, 0.9256897568702698, 0.9024756550788879]
2025-08-04 17:16:10 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 4: 检测到 3 个对象
2025-08-04 17:16:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:16:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.926, 位置: [1, 596, 490, 898]
2025-08-04 17:16:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:16:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.902, 位置: [1169, 223, 1426, 518]
2025-08-04 17:16:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:16:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.939, 位置: [1105, 507, 1348, 711]
2025-08-04 17:16:11 | INFO     | src.detection.detector:detect:223 | 检测调试 #5: 原始检测数=3, 过滤后检测数=3, 置信度阈值=0.5
2025-08-04 17:16:11 | INFO     | src.detection.detector:detect:225 | 推理时间: 6.5ms, 平均: 226.1ms
2025-08-04 17:16:11 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 17:16:11 | INFO     | src.detection.detector:detect:233 | 原始类别: [2.0, 0.0, 1.0]
2025-08-04 17:16:11 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.9389451146125793, 0.9274168610572815, 0.9030306935310364]
2025-08-04 17:16:11 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 5: 检测到 3 个对象
2025-08-04 17:16:11 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:16:11 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.927, 位置: [1, 596, 490, 898]
2025-08-04 17:16:11 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:16:11 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.903, 位置: [1169, 223, 1426, 518]
2025-08-04 17:16:11 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:16:11 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.939, 位置: [1104, 507, 1348, 711]
2025-08-04 17:16:11 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 6: 检测到 3 个对象
2025-08-04 17:16:11 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:16:11 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.926, 位置: [1, 596, 490, 898]
2025-08-04 17:16:11 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:16:11 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.903, 位置: [1168, 223, 1426, 518]
2025-08-04 17:16:11 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:16:11 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.936, 位置: [1104, 507, 1348, 711]
2025-08-04 17:16:11 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 7: 检测到 3 个对象
2025-08-04 17:16:11 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:16:11 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.926, 位置: [1, 596, 490, 898]
2025-08-04 17:16:11 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:16:11 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.902, 位置: [1169, 223, 1426, 518]
2025-08-04 17:16:11 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:16:11 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.943, 位置: [1105, 507, 1348, 711]
2025-08-04 17:16:11 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 8: 检测到 3 个对象
2025-08-04 17:16:11 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:16:11 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.923, 位置: [1, 596, 490, 898]
2025-08-04 17:16:11 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:16:11 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.901, 位置: [1169, 223, 1426, 518]
2025-08-04 17:16:11 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:16:11 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.943, 位置: [1105, 507, 1348, 711]
2025-08-04 17:16:11 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 9: 检测到 3 个对象
2025-08-04 17:16:11 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:16:11 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.923, 位置: [1, 596, 489, 898]
2025-08-04 17:16:11 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:16:11 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.899, 位置: [1169, 223, 1426, 518]
2025-08-04 17:16:11 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:16:11 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.943, 位置: [1105, 507, 1348, 711]
2025-08-04 17:16:11 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 10: 检测到 3 个对象
2025-08-04 17:16:11 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:16:11 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.921, 位置: [1, 596, 489, 898]
2025-08-04 17:16:11 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:16:11 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.900, 位置: [1168, 223, 1426, 518]
2025-08-04 17:16:11 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:16:11 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.942, 位置: [1105, 507, 1348, 711]
2025-08-04 17:16:14 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 100: 检测到 4 个对象
2025-08-04 17:16:14 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - hand: 1 个
2025-08-04 17:16:14 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.574, 位置: [296, 601, 439, 718]
2025-08-04 17:16:14 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:16:14 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.865, 位置: [0, 572, 488, 898]
2025-08-04 17:16:14 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:16:14 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.906, 位置: [1168, 224, 1426, 518]
2025-08-04 17:16:14 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:16:14 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.941, 位置: [1104, 507, 1348, 711]
2025-08-04 17:16:14 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与blue_box交互，IoU: 0.105, 距离: 144.9
2025-08-04 17:16:14 | INFO     | src.state_machine.action_state_machine:_handle_detecting_state:130 | 手部与blue_box的交互中断
2025-08-04 17:16:17 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 200: 检测到 3 个对象
2025-08-04 17:16:17 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:16:17 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.930, 位置: [0, 586, 492, 898]
2025-08-04 17:16:17 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:16:17 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.900, 位置: [1168, 224, 1426, 518]
2025-08-04 17:16:17 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:16:17 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.944, 位置: [1106, 507, 1348, 711]
2025-08-04 17:16:20 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与blue_box交互，IoU: 0.135, 距离: 157.3
2025-08-04 17:16:20 | SUCCESS  | src.state_machine.action_state_machine:_handle_detecting_state:155 | 检测到ok_action！第1次动作，持续时间: 0.50秒
2025-08-04 17:16:20 | SUCCESS  | src.state_machine.action_detector:_handle_action_detected:198 | 检测到动作: action_1754298980_1 (类型: ok_action)
2025-08-04 17:16:20 | SUCCESS  | src.utils.video_buffer:save_action_video:117 | 动作视频已保存: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_ok/action_1754298980_1_20250804_171620.mp4 (31帧)
2025-08-04 17:16:20 | SUCCESS  | src.state_machine.action_detector:_handle_action_detected:206 | 动作视频已保存: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_ok/action_1754298980_1_20250804_171620.mp4
2025-08-04 17:16:20 | ERROR    | src.state_machine.action_detector:_save_action_image:246 | 保存动作图像失败: Unknown format code 'd' for object of type 'str'
2025-08-04 17:16:20 | SUCCESS  | __main__:action_callback:132 | 🎯 检测到动作 #action_1754298980_1
2025-08-04 17:16:20 | INFO     | __main__:action_callback:133 |    动作类型: ✅ OK动作 - 产品放入蓝色箱子
2025-08-04 17:16:20 | INFO     | __main__:action_callback:134 |    容器类型: blue_box
2025-08-04 17:16:20 | INFO     | __main__:action_callback:135 |    时间戳: 17:16:20
2025-08-04 17:16:20 | INFO     | __main__:action_callback:139 |    交互时长: 0.50秒
2025-08-04 17:16:20 | INFO     | __main__:action_callback:140 |    IoU: 0.128
2025-08-04 17:16:20 | INFO     | __main__:action_callback:141 |    距离: 18.9px
2025-08-04 17:16:20 | INFO     | __main__:action_callback:142 |    置信度: 0.644
2025-08-04 17:16:20 | INFO     | __main__:action_callback:145 |    保存路径: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_ok/
2025-08-04 17:16:21 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 300: 检测到 3 个对象
2025-08-04 17:16:21 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:16:21 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.946, 位置: [4, 616, 478, 898]
2025-08-04 17:16:21 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:16:21 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.903, 位置: [1169, 223, 1426, 518]
2025-08-04 17:16:21 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:16:21 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.933, 位置: [1103, 507, 1348, 710]
2025-08-04 17:16:21 | INFO     | src.state_machine.action_detector:_log_statistics:397 | 统计信息 - 帧数: 300, 运行时间: 11.3s, FPS: 26.4, 状态: idle, 动作数: 1
2025-08-04 17:16:21 | INFO     | src.state_machine.action_detector:_log_statistics:405 | 检测统计 - 有检测的帧: 300/300 (100.0%), 总检测数: 933, 手: 33, 蓝色箱子: 300, 上层黄色箱子: 300, 下层黄色箱子: 300
2025-08-04 17:16:21 | INFO     | src.state_machine.action_detector:_log_statistics:415 | 性能统计 - 平均推理时间: 16.1ms, 估计FPS: 62.1, 设备: 0
2025-08-04 17:16:21 | INFO     | src.state_machine.action_detector:_log_statistics:420 | GPU内存 - 已分配: 43.5MB, 已缓存: 90.0MB
2025-08-04 17:16:24 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 400: 检测到 3 个对象
2025-08-04 17:16:24 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:16:24 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.913, 位置: [1, 597, 490, 898]
2025-08-04 17:16:24 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:16:24 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.906, 位置: [1169, 223, 1426, 518]
2025-08-04 17:16:24 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:16:24 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.944, 位置: [1105, 508, 1348, 711]
2025-08-04 17:16:27 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 500: 检测到 3 个对象
2025-08-04 17:16:27 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:16:27 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.924, 位置: [2, 597, 490, 898]
2025-08-04 17:16:27 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:16:27 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.900, 位置: [1168, 224, 1426, 518]
2025-08-04 17:16:27 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:16:27 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.940, 位置: [1105, 507, 1348, 710]
2025-08-04 17:16:31 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 600: 检测到 3 个对象
2025-08-04 17:16:31 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:16:31 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.924, 位置: [1, 597, 491, 898]
2025-08-04 17:16:31 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:16:31 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.876, 位置: [1169, 224, 1426, 518]
2025-08-04 17:16:31 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:16:31 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.948, 位置: [1104, 507, 1348, 711]
2025-08-04 17:16:31 | INFO     | src.state_machine.action_detector:_log_statistics:397 | 统计信息 - 帧数: 600, 运行时间: 21.4s, FPS: 28.1, 状态: idle, 动作数: 1
2025-08-04 17:16:31 | INFO     | src.state_machine.action_detector:_log_statistics:405 | 检测统计 - 有检测的帧: 600/600 (100.0%), 总检测数: 1833, 手: 33, 蓝色箱子: 600, 上层黄色箱子: 600, 下层黄色箱子: 600
2025-08-04 17:16:31 | INFO     | src.state_machine.action_detector:_log_statistics:415 | 性能统计 - 平均推理时间: 14.5ms, 估计FPS: 68.9, 设备: 0
2025-08-04 17:16:31 | INFO     | src.state_machine.action_detector:_log_statistics:420 | GPU内存 - 已分配: 43.5MB, 已缓存: 90.0MB
2025-08-04 17:16:34 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 700: 检测到 3 个对象
2025-08-04 17:16:34 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:16:34 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.929, 位置: [1, 596, 490, 898]
2025-08-04 17:16:34 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:16:34 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.892, 位置: [1168, 223, 1425, 518]
2025-08-04 17:16:34 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:16:34 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.942, 位置: [1105, 508, 1348, 711]
2025-08-04 17:16:37 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 800: 检测到 3 个对象
2025-08-04 17:16:37 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:16:37 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.921, 位置: [1, 596, 490, 898]
2025-08-04 17:16:37 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:16:37 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.905, 位置: [1168, 223, 1426, 518]
2025-08-04 17:16:37 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:16:37 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.943, 位置: [1106, 508, 1348, 711]
2025-08-04 17:16:41 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 900: 检测到 3 个对象
2025-08-04 17:16:41 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:16:41 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.925, 位置: [1, 597, 490, 898]
2025-08-04 17:16:41 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:16:41 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.905, 位置: [1168, 223, 1426, 518]
2025-08-04 17:16:41 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:16:41 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.928, 位置: [1105, 507, 1348, 711]
2025-08-04 17:16:41 | INFO     | src.state_machine.action_detector:_log_statistics:397 | 统计信息 - 帧数: 900, 运行时间: 31.4s, FPS: 28.6, 状态: idle, 动作数: 1
2025-08-04 17:16:41 | INFO     | src.state_machine.action_detector:_log_statistics:405 | 检测统计 - 有检测的帧: 900/900 (100.0%), 总检测数: 2733, 手: 33, 蓝色箱子: 900, 上层黄色箱子: 900, 下层黄色箱子: 900
2025-08-04 17:16:41 | INFO     | src.state_machine.action_detector:_log_statistics:415 | 性能统计 - 平均推理时间: 13.9ms, 估计FPS: 72.1, 设备: 0
2025-08-04 17:16:41 | INFO     | src.state_machine.action_detector:_log_statistics:420 | GPU内存 - 已分配: 43.5MB, 已缓存: 90.0MB
2025-08-04 17:16:44 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 1000: 检测到 3 个对象
2025-08-04 17:16:44 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:16:44 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.926, 位置: [1, 596, 490, 898]
2025-08-04 17:16:44 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:16:44 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.906, 位置: [1168, 224, 1426, 518]
2025-08-04 17:16:44 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:16:44 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.930, 位置: [1107, 507, 1348, 711]
2025-08-04 17:16:47 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 1100: 检测到 3 个对象
2025-08-04 17:16:47 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:16:47 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.923, 位置: [1, 596, 490, 898]
2025-08-04 17:16:47 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:16:47 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.907, 位置: [1168, 223, 1426, 518]
2025-08-04 17:16:47 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:16:47 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.939, 位置: [1104, 508, 1348, 710]
2025-08-04 17:16:51 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 1200: 检测到 3 个对象
2025-08-04 17:16:51 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:16:51 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.926, 位置: [1, 596, 490, 898]
2025-08-04 17:16:51 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:16:51 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.882, 位置: [1169, 223, 1426, 518]
2025-08-04 17:16:51 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:16:51 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.934, 位置: [1108, 508, 1348, 711]
2025-08-04 17:16:51 | INFO     | src.state_machine.action_detector:_log_statistics:397 | 统计信息 - 帧数: 1200, 运行时间: 41.4s, FPS: 29.0, 状态: idle, 动作数: 1
2025-08-04 17:16:51 | INFO     | src.state_machine.action_detector:_log_statistics:405 | 检测统计 - 有检测的帧: 1200/1200 (100.0%), 总检测数: 3634, 手: 34, 蓝色箱子: 1200, 上层黄色箱子: 1200, 下层黄色箱子: 1200
2025-08-04 17:16:51 | INFO     | src.state_machine.action_detector:_log_statistics:415 | 性能统计 - 平均推理时间: 13.5ms, 估计FPS: 73.9, 设备: 0
2025-08-04 17:16:51 | INFO     | src.state_machine.action_detector:_log_statistics:420 | GPU内存 - 已分配: 43.5MB, 已缓存: 90.0MB
2025-08-04 17:16:54 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 1300: 检测到 3 个对象
2025-08-04 17:16:54 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:16:54 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.924, 位置: [1, 596, 491, 898]
2025-08-04 17:16:54 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:16:54 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.880, 位置: [1168, 223, 1426, 518]
2025-08-04 17:16:54 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:16:54 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.930, 位置: [1107, 508, 1348, 711]
2025-08-04 17:16:57 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 1400: 检测到 3 个对象
2025-08-04 17:16:57 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:16:57 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.919, 位置: [1, 596, 491, 898]
2025-08-04 17:16:57 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:16:57 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.884, 位置: [1168, 223, 1425, 518]
2025-08-04 17:16:57 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:16:57 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.934, 位置: [1106, 508, 1348, 711]
2025-08-04 17:17:01 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 1500: 检测到 4 个对象
2025-08-04 17:17:01 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - hand: 1 个
2025-08-04 17:17:01 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.661, 位置: [896, 479, 1044, 563]
2025-08-04 17:17:01 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:17:01 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.935, 位置: [1, 596, 491, 898]
2025-08-04 17:17:01 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:17:01 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.889, 位置: [1168, 223, 1426, 518]
2025-08-04 17:17:01 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:17:01 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.937, 位置: [1107, 508, 1348, 711]
2025-08-04 17:17:01 | INFO     | src.state_machine.action_detector:_log_statistics:397 | 统计信息 - 帧数: 1500, 运行时间: 51.5s, FPS: 29.1, 状态: idle, 动作数: 1
2025-08-04 17:17:01 | INFO     | src.state_machine.action_detector:_log_statistics:405 | 检测统计 - 有检测的帧: 1500/1500 (100.0%), 总检测数: 4582, 手: 82, 蓝色箱子: 1500, 上层黄色箱子: 1500, 下层黄色箱子: 1500
2025-08-04 17:17:01 | INFO     | src.state_machine.action_detector:_log_statistics:415 | 性能统计 - 平均推理时间: 13.4ms, 估计FPS: 74.8, 设备: 0
2025-08-04 17:17:01 | INFO     | src.state_machine.action_detector:_log_statistics:420 | GPU内存 - 已分配: 43.5MB, 已缓存: 90.0MB
2025-08-04 17:17:04 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 1600: 检测到 3 个对象
2025-08-04 17:17:04 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:17:04 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.928, 位置: [1, 597, 490, 898]
2025-08-04 17:17:04 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:17:04 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.897, 位置: [1169, 223, 1426, 518]
2025-08-04 17:17:04 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:17:04 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.940, 位置: [1107, 507, 1348, 711]
2025-08-04 17:17:05 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与blue_box交互，IoU: 0.121, 距离: 145.2
2025-08-04 17:17:05 | SUCCESS  | src.state_machine.action_state_machine:_handle_detecting_state:155 | 检测到ok_action！第2次动作，持续时间: 0.50秒
2025-08-04 17:17:05 | SUCCESS  | src.state_machine.action_detector:_handle_action_detected:198 | 检测到动作: action_1754299025_2 (类型: ok_action)
2025-08-04 17:17:05 | SUCCESS  | src.utils.video_buffer:save_action_video:117 | 动作视频已保存: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_ok/action_1754299025_2_20250804_171705.mp4 (31帧)
2025-08-04 17:17:05 | SUCCESS  | src.state_machine.action_detector:_handle_action_detected:206 | 动作视频已保存: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_ok/action_1754299025_2_20250804_171705.mp4
2025-08-04 17:17:05 | ERROR    | src.state_machine.action_detector:_save_action_image:246 | 保存动作图像失败: Unknown format code 'd' for object of type 'str'
2025-08-04 17:17:05 | SUCCESS  | __main__:action_callback:132 | 🎯 检测到动作 #action_1754299025_2
2025-08-04 17:17:05 | INFO     | __main__:action_callback:133 |    动作类型: ✅ OK动作 - 产品放入蓝色箱子
2025-08-04 17:17:05 | INFO     | __main__:action_callback:134 |    容器类型: blue_box
2025-08-04 17:17:05 | INFO     | __main__:action_callback:135 |    时间戳: 17:17:05
2025-08-04 17:17:05 | INFO     | __main__:action_callback:139 |    交互时长: 0.50秒
2025-08-04 17:17:05 | INFO     | __main__:action_callback:140 |    IoU: 0.119
2025-08-04 17:17:05 | INFO     | __main__:action_callback:141 |    距离: 63.3px
2025-08-04 17:17:05 | INFO     | __main__:action_callback:142 |    置信度: 0.637
2025-08-04 17:17:05 | INFO     | __main__:action_callback:145 |    保存路径: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_ok/
2025-08-04 17:17:08 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 1700: 检测到 3 个对象
2025-08-04 17:17:08 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:17:08 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.938, 位置: [1, 598, 490, 898]
2025-08-04 17:17:08 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:17:08 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.893, 位置: [1169, 223, 1426, 518]
2025-08-04 17:17:08 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:17:08 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.943, 位置: [1104, 508, 1348, 711]
2025-08-04 17:17:11 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 1800: 检测到 4 个对象
2025-08-04 17:17:11 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - hand: 1 个
2025-08-04 17:17:11 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.554, 位置: [880, 456, 1036, 570]
2025-08-04 17:17:11 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:17:11 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.934, 位置: [1, 596, 490, 898]
2025-08-04 17:17:11 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:17:11 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.888, 位置: [1168, 224, 1426, 518]
2025-08-04 17:17:11 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:17:11 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.942, 位置: [1106, 508, 1348, 711]
2025-08-04 17:17:11 | INFO     | src.state_machine.action_detector:_log_statistics:397 | 统计信息 - 帧数: 1800, 运行时间: 61.7s, FPS: 29.2, 状态: idle, 动作数: 2
2025-08-04 17:17:11 | INFO     | src.state_machine.action_detector:_log_statistics:405 | 检测统计 - 有检测的帧: 1800/1800 (100.0%), 总检测数: 5502, 手: 102, 蓝色箱子: 1800, 上层黄色箱子: 1800, 下层黄色箱子: 1800
2025-08-04 17:17:11 | INFO     | src.state_machine.action_detector:_log_statistics:415 | 性能统计 - 平均推理时间: 13.2ms, 估计FPS: 75.6, 设备: 0
2025-08-04 17:17:11 | INFO     | src.state_machine.action_detector:_log_statistics:420 | GPU内存 - 已分配: 43.5MB, 已缓存: 90.0MB
2025-08-04 17:17:14 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 1900: 检测到 3 个对象
2025-08-04 17:17:14 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:17:14 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.930, 位置: [1, 597, 491, 898]
2025-08-04 17:17:14 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:17:14 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.889, 位置: [1165, 223, 1427, 518]
2025-08-04 17:17:14 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:17:14 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.948, 位置: [1104, 508, 1348, 711]
2025-08-04 17:17:18 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 2000: 检测到 3 个对象
2025-08-04 17:17:18 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:17:18 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.935, 位置: [1, 596, 490, 898]
2025-08-04 17:17:18 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:17:18 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.897, 位置: [1169, 223, 1426, 518]
2025-08-04 17:17:18 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:17:18 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.942, 位置: [1105, 507, 1348, 711]
2025-08-04 17:17:19 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与blue_box交互，IoU: 0.104, 距离: 142.1
2025-08-04 17:17:19 | INFO     | src.state_machine.action_state_machine:_handle_detecting_state:130 | 手部与blue_box的交互中断
2025-08-04 17:17:19 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与blue_box交互，IoU: 0.134, 距离: 59.6
2025-08-04 17:17:19 | INFO     | src.state_machine.action_state_machine:_handle_detecting_state:130 | 手部与blue_box的交互中断
2025-08-04 17:17:19 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与blue_box交互，IoU: 0.126, 距离: 63.2
2025-08-04 17:17:19 | INFO     | src.state_machine.action_state_machine:_handle_detecting_state:130 | 手部与blue_box的交互中断
2025-08-04 17:17:21 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 2100: 检测到 3 个对象
2025-08-04 17:17:21 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:17:21 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.935, 位置: [1, 597, 491, 898]
2025-08-04 17:17:21 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:17:21 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.907, 位置: [1168, 223, 1426, 518]
2025-08-04 17:17:21 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:17:21 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.933, 位置: [1107, 507, 1348, 711]
2025-08-04 17:17:21 | INFO     | src.state_machine.action_detector:_log_statistics:397 | 统计信息 - 帧数: 2100, 运行时间: 71.7s, FPS: 29.3, 状态: idle, 动作数: 2
2025-08-04 17:17:21 | INFO     | src.state_machine.action_detector:_log_statistics:405 | 检测统计 - 有检测的帧: 2100/2100 (100.0%), 总检测数: 6436, 手: 136, 蓝色箱子: 2100, 上层黄色箱子: 2100, 下层黄色箱子: 2100
2025-08-04 17:17:21 | INFO     | src.state_machine.action_detector:_log_statistics:415 | 性能统计 - 平均推理时间: 13.1ms, 估计FPS: 76.1, 设备: 0
2025-08-04 17:17:21 | INFO     | src.state_machine.action_detector:_log_statistics:420 | GPU内存 - 已分配: 43.5MB, 已缓存: 90.0MB
2025-08-04 17:17:24 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 2200: 检测到 3 个对象
2025-08-04 17:17:24 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:17:24 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.945, 位置: [1, 596, 490, 898]
2025-08-04 17:17:24 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:17:24 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.900, 位置: [1168, 223, 1426, 518]
2025-08-04 17:17:24 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:17:24 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.951, 位置: [1105, 507, 1348, 710]
2025-08-04 17:17:28 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 2300: 检测到 3 个对象
2025-08-04 17:17:28 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:17:28 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.937, 位置: [0, 587, 492, 898]
2025-08-04 17:17:28 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:17:28 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.898, 位置: [1168, 223, 1426, 518]
2025-08-04 17:17:28 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:17:28 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.931, 位置: [1104, 508, 1348, 711]
2025-08-04 17:17:31 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 2400: 检测到 3 个对象
2025-08-04 17:17:31 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:17:31 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.932, 位置: [1, 597, 490, 898]
2025-08-04 17:17:31 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:17:31 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.880, 位置: [1168, 223, 1426, 518]
2025-08-04 17:17:31 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:17:31 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.935, 位置: [1105, 508, 1348, 711]
2025-08-04 17:17:31 | INFO     | src.state_machine.action_detector:_log_statistics:397 | 统计信息 - 帧数: 2400, 运行时间: 81.7s, FPS: 29.4, 状态: idle, 动作数: 2
2025-08-04 17:17:31 | INFO     | src.state_machine.action_detector:_log_statistics:405 | 检测统计 - 有检测的帧: 2400/2400 (100.0%), 总检测数: 7340, 手: 140, 蓝色箱子: 2400, 上层黄色箱子: 2400, 下层黄色箱子: 2400
2025-08-04 17:17:31 | INFO     | src.state_machine.action_detector:_log_statistics:415 | 性能统计 - 平均推理时间: 13.1ms, 估计FPS: 76.5, 设备: 0
2025-08-04 17:17:31 | INFO     | src.state_machine.action_detector:_log_statistics:420 | GPU内存 - 已分配: 43.5MB, 已缓存: 90.0MB
2025-08-04 17:17:34 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 2500: 检测到 3 个对象
2025-08-04 17:17:34 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:17:34 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.934, 位置: [1, 596, 490, 898]
2025-08-04 17:17:34 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:17:34 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.867, 位置: [1168, 224, 1425, 518]
2025-08-04 17:17:34 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:17:34 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.938, 位置: [1107, 508, 1348, 710]
2025-08-04 17:17:38 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 2600: 检测到 3 个对象
2025-08-04 17:17:38 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:17:38 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.936, 位置: [2, 596, 491, 898]
2025-08-04 17:17:38 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:17:38 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.895, 位置: [1168, 223, 1425, 518]
2025-08-04 17:17:38 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:17:38 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.947, 位置: [1104, 508, 1348, 711]
2025-08-04 17:17:41 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 2700: 检测到 3 个对象
2025-08-04 17:17:41 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:17:41 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.932, 位置: [1, 596, 490, 898]
2025-08-04 17:17:41 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:17:41 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.904, 位置: [1168, 223, 1426, 518]
2025-08-04 17:17:41 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:17:41 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.948, 位置: [1104, 507, 1348, 711]
2025-08-04 17:17:41 | INFO     | src.state_machine.action_detector:_log_statistics:397 | 统计信息 - 帧数: 2700, 运行时间: 91.8s, FPS: 29.4, 状态: idle, 动作数: 2
2025-08-04 17:17:41 | INFO     | src.state_machine.action_detector:_log_statistics:405 | 检测统计 - 有检测的帧: 2700/2700 (100.0%), 总检测数: 8240, 手: 140, 蓝色箱子: 2700, 上层黄色箱子: 2700, 下层黄色箱子: 2700
2025-08-04 17:17:41 | INFO     | src.state_machine.action_detector:_log_statistics:415 | 性能统计 - 平均推理时间: 13.0ms, 估计FPS: 77.0, 设备: 0
2025-08-04 17:17:41 | INFO     | src.state_machine.action_detector:_log_statistics:420 | GPU内存 - 已分配: 43.5MB, 已缓存: 90.0MB
2025-08-04 17:17:44 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 2800: 检测到 3 个对象
2025-08-04 17:17:44 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:17:44 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.931, 位置: [1, 597, 491, 898]
2025-08-04 17:17:44 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:17:44 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.871, 位置: [1168, 224, 1426, 518]
2025-08-04 17:17:44 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:17:44 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.951, 位置: [1105, 508, 1348, 711]
2025-08-04 17:17:48 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 2900: 检测到 3 个对象
2025-08-04 17:17:48 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:17:48 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.937, 位置: [1, 596, 490, 898]
2025-08-04 17:17:48 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:17:48 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.888, 位置: [1168, 224, 1426, 518]
2025-08-04 17:17:48 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:17:48 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.956, 位置: [1107, 508, 1348, 711]
2025-08-04 17:17:51 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 3000: 检测到 3 个对象
2025-08-04 17:17:51 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:17:51 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.932, 位置: [1, 597, 490, 898]
2025-08-04 17:17:51 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:17:51 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.894, 位置: [1168, 223, 1426, 518]
2025-08-04 17:17:51 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:17:51 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.947, 位置: [1104, 508, 1348, 711]
2025-08-04 17:17:51 | INFO     | src.state_machine.action_detector:_log_statistics:397 | 统计信息 - 帧数: 3000, 运行时间: 101.8s, FPS: 29.5, 状态: idle, 动作数: 2
2025-08-04 17:17:51 | INFO     | src.state_machine.action_detector:_log_statistics:405 | 检测统计 - 有检测的帧: 3000/3000 (100.0%), 总检测数: 9140, 手: 140, 蓝色箱子: 3000, 上层黄色箱子: 3000, 下层黄色箱子: 3000
2025-08-04 17:17:51 | INFO     | src.state_machine.action_detector:_log_statistics:415 | 性能统计 - 平均推理时间: 13.0ms, 估计FPS: 77.0, 设备: 0
2025-08-04 17:17:51 | INFO     | src.state_machine.action_detector:_log_statistics:420 | GPU内存 - 已分配: 43.5MB, 已缓存: 90.0MB
2025-08-04 17:17:54 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 3100: 检测到 3 个对象
2025-08-04 17:17:54 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:17:54 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.938, 位置: [1, 597, 490, 898]
2025-08-04 17:17:54 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:17:54 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.903, 位置: [1168, 223, 1426, 518]
2025-08-04 17:17:54 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:17:54 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.950, 位置: [1105, 508, 1348, 711]
2025-08-04 17:17:58 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 3200: 检测到 3 个对象
2025-08-04 17:17:58 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:17:58 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.932, 位置: [1, 596, 491, 898]
2025-08-04 17:17:58 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:17:58 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.893, 位置: [1168, 224, 1425, 518]
2025-08-04 17:17:58 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:17:58 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.957, 位置: [1105, 508, 1348, 711]
2025-08-04 17:18:01 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 3300: 检测到 3 个对象
2025-08-04 17:18:01 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:18:01 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.934, 位置: [1, 596, 491, 898]
2025-08-04 17:18:01 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:18:01 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.891, 位置: [1168, 223, 1425, 518]
2025-08-04 17:18:01 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:18:01 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.954, 位置: [1106, 508, 1348, 711]
2025-08-04 17:18:01 | INFO     | src.state_machine.action_detector:_log_statistics:397 | 统计信息 - 帧数: 3300, 运行时间: 111.8s, FPS: 29.5, 状态: idle, 动作数: 2
2025-08-04 17:18:01 | INFO     | src.state_machine.action_detector:_log_statistics:405 | 检测统计 - 有检测的帧: 3300/3300 (100.0%), 总检测数: 10040, 手: 140, 蓝色箱子: 3300, 上层黄色箱子: 3300, 下层黄色箱子: 3300
2025-08-04 17:18:01 | INFO     | src.state_machine.action_detector:_log_statistics:415 | 性能统计 - 平均推理时间: 12.9ms, 估计FPS: 77.2, 设备: 0
2025-08-04 17:18:01 | INFO     | src.state_machine.action_detector:_log_statistics:420 | GPU内存 - 已分配: 43.5MB, 已缓存: 90.0MB
2025-08-04 17:18:04 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 3400: 检测到 3 个对象
2025-08-04 17:18:04 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:18:04 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.929, 位置: [1, 597, 490, 898]
2025-08-04 17:18:04 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:18:04 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.888, 位置: [1168, 223, 1426, 518]
2025-08-04 17:18:04 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:18:04 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.944, 位置: [1105, 508, 1348, 712]
2025-08-04 17:18:06 | INFO     | src.state_machine.action_detector:run:186 | 检测到键盘中断，正在退出...
2025-08-04 17:18:06 | INFO     | src.detection.video_capture:release:203 | 视频捕获资源已释放
2025-08-04 17:18:06 | INFO     | src.state_machine.action_detector:_cleanup:435 | 检测完成 - 总帧数: 3440, 总运行时间: 116.5s, 平均FPS: 29.5, 检测到的动作数: 2
2025-08-04 17:18:06 | INFO     | __main__:main:261 | ============================================================
2025-08-04 17:18:06 | INFO     | __main__:main:262 | 👋 程序结束
2025-08-04 17:18:06 | INFO     | __main__:main:263 | ============================================================
2025-08-04 17:18:08 | INFO     | src.utils.logger:setup_logger:55 | 日志系统初始化完成，级别: INFO
2025-08-04 17:18:08 | INFO     | src.utils.logger:setup_logger:57 | 日志文件: logs/action_detection.log
2025-08-04 17:18:08 | INFO     | __main__:main:170 | ============================================================
2025-08-04 17:18:08 | INFO     | __main__:main:171 | 🚀 YOLOv8实时动作检测系统启动
2025-08-04 17:18:08 | INFO     | __main__:main:172 | ============================================================
2025-08-04 17:18:08 | INFO     | __main__:main:178 | 使用命令行指定的视频文件: /home/<USER>/wolong_ws/yolo_detect_ws/data/test_videos/test_video_long.mp4
2025-08-04 17:18:08 | INFO     | __main__:main:189 | 📹 视频源: /home/<USER>/wolong_ws/yolo_detect_ws/data/test_videos/test_video_long.mp4
2025-08-04 17:18:08 | INFO     | __main__:main:190 | 🤖 模型路径: yolo_training/yolov8n_custom2/weights/best.pt
2025-08-04 17:18:08 | INFO     | __main__:main:191 | 📁 输出目录: output/detected_actions
2025-08-04 17:18:08 | INFO     | __main__:main:192 | 🎯 置信度阈值: 0.5
2025-08-04 17:18:08 | INFO     | __main__:main:193 | 📊 交互阈值 - IoU: 0.1, 距离: 50px
2025-08-04 17:18:08 | INFO     | __main__:main:194 | ⏰ 冷却时间: 3.0秒, 动作持续时间: 0.5秒
2025-08-04 17:18:08 | INFO     | src.detection.detector:_get_device:55 | torch.cuda.is_available(): True
2025-08-04 17:18:09 | INFO     | src.detection.detector:_get_device:56 | torch.cuda.device_count(): 1
2025-08-04 17:18:09 | INFO     | src.detection.detector:_get_device:62 | 检测到GPU: NVIDIA GeForce RTX 4060 Laptop GPU (7.6GB)
2025-08-04 17:18:09 | INFO     | src.detection.detector:_get_device:66 | 使用GPU设备: cuda:0
2025-08-04 17:18:09 | INFO     | src.detection.detector:_get_device:88 | 使用设备: 0
2025-08-04 17:18:09 | INFO     | src.detection.detector:_get_device:97 | GPU缓存已清理
2025-08-04 17:18:09 | INFO     | src.detection.detector:_get_device:101 | GPU内存分配策略已设置
2025-08-04 17:18:09 | INFO     | src.detection.detector:_load_model:117 | 加载模型: yolo_training/yolov8n_custom2/weights/best.pt
2025-08-04 17:18:09 | INFO     | src.detection.detector:_load_model:123 | 将模型移动到GPU设备: 0
2025-08-04 17:18:09 | INFO     | src.detection.detector:_load_model:129 | 模型已成功移动到GPU
2025-08-04 17:18:09 | INFO     | src.detection.detector:_load_model:138 | 检测类别: {0: 'blue_box', 1: 'upper_yellow_box', 2: 'lower_yellow_box', 3: 'hand'}
2025-08-04 17:18:09 | INFO     | src.detection.video_capture:_initialize_capture:52 | 初始化视频文件: /home/<USER>/wolong_ws/yolo_detect_ws/data/test_videos/test_video_long.mp4
2025-08-04 17:18:09 | INFO     | src.detection.video_capture:_log_video_info:74 | 视频信息 - 分辨率: 1442x898, FPS: 25.00
2025-08-04 17:18:09 | INFO     | src.detection.video_capture:_log_video_info:79 | 视频时长: 304.00秒, 总帧数: 7600
2025-08-04 17:18:09 | INFO     | src.state_machine.action_state_machine:__init__:73 | 状态机初始化完成，初始状态: idle
2025-08-04 17:18:09 | INFO     | src.utils.video_buffer:__init__:33 | 视频缓存器初始化完成，缓存大小: 31帧，帧率: 30.0fps
2025-08-04 17:18:09 | INFO     | src.utils.video_buffer:__init__:168 | 确保目录存在: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_ok
2025-08-04 17:18:09 | INFO     | src.utils.video_buffer:__init__:168 | 确保目录存在: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_nok_electric
2025-08-04 17:18:09 | INFO     | src.utils.video_buffer:__init__:168 | 确保目录存在: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_nok_appearence
2025-08-04 17:18:09 | INFO     | src.state_machine.action_detector:__init__:96 | 视频保存功能已启用，缓存大小: 31帧
2025-08-04 17:18:09 | INFO     | src.state_machine.action_detector:__init__:120 | 动作检测器初始化完成
2025-08-04 17:18:09 | INFO     | __main__:main:226 | 🖥️  推理设备: 0
2025-08-04 17:18:09 | INFO     | __main__:main:237 | 💡 控制提示:
2025-08-04 17:18:09 | INFO     | __main__:main:238 |    按 'q' 或 ESC 退出程序
2025-08-04 17:18:09 | INFO     | __main__:main:239 |    按 'r' 重置状态机
2025-08-04 17:18:09 | INFO     | __main__:main:240 |    按 'd' 切换调试模式
2025-08-04 17:18:09 | INFO     | __main__:main:242 | 🔍 开始检测...
2025-08-04 17:18:09 | INFO     | __main__:main:243 | ------------------------------------------------------------
2025-08-04 17:18:09 | INFO     | src.state_machine.action_detector:run:139 | 开始运行动作检测
2025-08-04 17:18:10 | INFO     | src.detection.detector:detect:223 | 检测调试 #1: 原始检测数=3, 过滤后检测数=3, 置信度阈值=0.5
2025-08-04 17:18:10 | INFO     | src.detection.detector:detect:225 | 推理时间: 1140.1ms, 平均: 1140.1ms
2025-08-04 17:18:10 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 17:18:10 | INFO     | src.detection.detector:detect:233 | 原始类别: [2.0, 0.0, 1.0]
2025-08-04 17:18:10 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.9483245611190796, 0.9287644624710083, 0.9003223180770874]
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 1: 检测到 3 个对象
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.929, 位置: [0, 586, 491, 898]
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.900, 位置: [1168, 223, 1426, 518]
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.948, 位置: [1105, 507, 1348, 711]
2025-08-04 17:18:10 | INFO     | src.detection.detector:detect:223 | 检测调试 #2: 原始检测数=3, 过滤后检测数=3, 置信度阈值=0.5
2025-08-04 17:18:10 | INFO     | src.detection.detector:detect:225 | 推理时间: 7.2ms, 平均: 573.6ms
2025-08-04 17:18:10 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 17:18:10 | INFO     | src.detection.detector:detect:233 | 原始类别: [2.0, 0.0, 1.0]
2025-08-04 17:18:10 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.9426160454750061, 0.92695152759552, 0.9040740132331848]
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 2: 检测到 3 个对象
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.927, 位置: [1, 596, 490, 898]
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.904, 位置: [1169, 223, 1426, 518]
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.943, 位置: [1104, 507, 1348, 711]
2025-08-04 17:18:10 | INFO     | src.detection.detector:detect:223 | 检测调试 #3: 原始检测数=3, 过滤后检测数=3, 置信度阈值=0.5
2025-08-04 17:18:10 | INFO     | src.detection.detector:detect:225 | 推理时间: 5.8ms, 平均: 384.4ms
2025-08-04 17:18:10 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 17:18:10 | INFO     | src.detection.detector:detect:233 | 原始类别: [2.0, 0.0, 1.0]
2025-08-04 17:18:10 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.9422558546066284, 0.9203870296478271, 0.9028117656707764]
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 3: 检测到 3 个对象
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.920, 位置: [1, 596, 490, 898]
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.903, 位置: [1169, 223, 1426, 518]
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.942, 位置: [1104, 507, 1348, 711]
2025-08-04 17:18:10 | INFO     | src.detection.detector:detect:223 | 检测调试 #4: 原始检测数=3, 过滤后检测数=3, 置信度阈值=0.5
2025-08-04 17:18:10 | INFO     | src.detection.detector:detect:225 | 推理时间: 7.4ms, 平均: 290.1ms
2025-08-04 17:18:10 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 17:18:10 | INFO     | src.detection.detector:detect:233 | 原始类别: [2.0, 0.0, 1.0]
2025-08-04 17:18:10 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.9385360479354858, 0.9256897568702698, 0.9024756550788879]
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 4: 检测到 3 个对象
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.926, 位置: [1, 596, 490, 898]
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.902, 位置: [1169, 223, 1426, 518]
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.939, 位置: [1105, 507, 1348, 711]
2025-08-04 17:18:10 | INFO     | src.detection.detector:detect:223 | 检测调试 #5: 原始检测数=3, 过滤后检测数=3, 置信度阈值=0.5
2025-08-04 17:18:10 | INFO     | src.detection.detector:detect:225 | 推理时间: 6.3ms, 平均: 233.3ms
2025-08-04 17:18:10 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 17:18:10 | INFO     | src.detection.detector:detect:233 | 原始类别: [2.0, 0.0, 1.0]
2025-08-04 17:18:10 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.9389451146125793, 0.9274168610572815, 0.9030306935310364]
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 5: 检测到 3 个对象
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.927, 位置: [1, 596, 490, 898]
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.903, 位置: [1169, 223, 1426, 518]
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.939, 位置: [1104, 507, 1348, 711]
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 6: 检测到 3 个对象
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.926, 位置: [1, 596, 490, 898]
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.903, 位置: [1168, 223, 1426, 518]
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.936, 位置: [1104, 507, 1348, 711]
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 7: 检测到 3 个对象
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.926, 位置: [1, 596, 490, 898]
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.902, 位置: [1169, 223, 1426, 518]
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.943, 位置: [1105, 507, 1348, 711]
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 8: 检测到 3 个对象
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.923, 位置: [1, 596, 490, 898]
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.901, 位置: [1169, 223, 1426, 518]
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.943, 位置: [1105, 507, 1348, 711]
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 9: 检测到 3 个对象
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.923, 位置: [1, 596, 489, 898]
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.899, 位置: [1169, 223, 1426, 518]
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.943, 位置: [1105, 507, 1348, 711]
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 10: 检测到 3 个对象
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.921, 位置: [1, 596, 489, 898]
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.900, 位置: [1168, 223, 1426, 518]
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:18:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.942, 位置: [1105, 507, 1348, 711]
2025-08-04 17:18:13 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 100: 检测到 4 个对象
2025-08-04 17:18:13 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - hand: 1 个
2025-08-04 17:18:13 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.574, 位置: [296, 601, 439, 718]
2025-08-04 17:18:13 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:18:13 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.865, 位置: [0, 572, 488, 898]
2025-08-04 17:18:13 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:18:13 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.906, 位置: [1168, 224, 1426, 518]
2025-08-04 17:18:13 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:18:13 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.941, 位置: [1104, 507, 1348, 711]
2025-08-04 17:18:13 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与blue_box交互，IoU: 0.105, 距离: 144.9
2025-08-04 17:18:14 | INFO     | src.state_machine.action_state_machine:_handle_detecting_state:130 | 手部与blue_box的交互中断
2025-08-04 17:18:17 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 200: 检测到 3 个对象
2025-08-04 17:18:17 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:18:17 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.930, 位置: [0, 586, 492, 898]
2025-08-04 17:18:17 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:18:17 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.900, 位置: [1168, 224, 1426, 518]
2025-08-04 17:18:17 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:18:17 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.944, 位置: [1106, 507, 1348, 711]
2025-08-04 17:18:19 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与blue_box交互，IoU: 0.135, 距离: 157.3
2025-08-04 17:18:20 | SUCCESS  | src.state_machine.action_state_machine:_handle_detecting_state:155 | 检测到ok_action！第1次动作，持续时间: 0.50秒
2025-08-04 17:18:20 | SUCCESS  | src.state_machine.action_detector:_handle_action_detected:198 | 检测到动作: action_1754299100_1 (类型: ok_action)
2025-08-04 17:18:20 | SUCCESS  | src.utils.video_buffer:save_action_video:117 | 动作视频已保存: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_ok/action_1754299100_1_20250804_171820.mp4 (31帧)
2025-08-04 17:18:20 | SUCCESS  | src.state_machine.action_detector:_handle_action_detected:206 | 动作视频已保存: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_ok/action_1754299100_1_20250804_171820.mp4
2025-08-04 17:18:20 | ERROR    | src.state_machine.action_detector:_save_action_image:246 | 保存动作图像失败: Unknown format code 'd' for object of type 'str'
2025-08-04 17:18:20 | SUCCESS  | __main__:action_callback:132 | 🎯 检测到动作 #action_1754299100_1
2025-08-04 17:18:20 | INFO     | __main__:action_callback:133 |    动作类型: ✅ OK动作 - 产品放入蓝色箱子
2025-08-04 17:18:20 | INFO     | __main__:action_callback:134 |    容器类型: blue_box
2025-08-04 17:18:20 | INFO     | __main__:action_callback:135 |    时间戳: 17:18:20
2025-08-04 17:18:20 | INFO     | __main__:action_callback:139 |    交互时长: 0.50秒
2025-08-04 17:18:20 | INFO     | __main__:action_callback:140 |    IoU: 0.128
2025-08-04 17:18:20 | INFO     | __main__:action_callback:141 |    距离: 18.9px
2025-08-04 17:18:20 | INFO     | __main__:action_callback:142 |    置信度: 0.644
2025-08-04 17:18:20 | INFO     | __main__:action_callback:145 |    保存路径: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_ok/
2025-08-04 17:18:20 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 300: 检测到 3 个对象
2025-08-04 17:18:20 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:18:20 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.946, 位置: [4, 616, 478, 898]
2025-08-04 17:18:20 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:18:20 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.903, 位置: [1169, 223, 1426, 518]
2025-08-04 17:18:20 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:18:20 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.933, 位置: [1103, 507, 1348, 710]
2025-08-04 17:18:20 | INFO     | src.state_machine.action_detector:_log_statistics:397 | 统计信息 - 帧数: 300, 运行时间: 11.4s, FPS: 26.3, 状态: idle, 动作数: 1
2025-08-04 17:18:20 | INFO     | src.state_machine.action_detector:_log_statistics:405 | 检测统计 - 有检测的帧: 300/300 (100.0%), 总检测数: 933, 手: 33, 蓝色箱子: 300, 上层黄色箱子: 300, 下层黄色箱子: 300
2025-08-04 17:18:20 | INFO     | src.state_machine.action_detector:_log_statistics:415 | 性能统计 - 平均推理时间: 16.9ms, 估计FPS: 59.1, 设备: 0
2025-08-04 17:18:20 | INFO     | src.state_machine.action_detector:_log_statistics:420 | GPU内存 - 已分配: 43.5MB, 已缓存: 90.0MB
2025-08-04 17:18:23 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 400: 检测到 3 个对象
2025-08-04 17:18:23 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:18:23 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.913, 位置: [1, 597, 490, 898]
2025-08-04 17:18:23 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:18:23 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.906, 位置: [1169, 223, 1426, 518]
2025-08-04 17:18:23 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:18:23 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.944, 位置: [1105, 508, 1348, 711]
2025-08-04 17:18:27 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 500: 检测到 3 个对象
2025-08-04 17:18:27 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:18:27 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.924, 位置: [2, 597, 490, 898]
2025-08-04 17:18:27 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:18:27 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.900, 位置: [1168, 224, 1426, 518]
2025-08-04 17:18:27 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:18:27 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.940, 位置: [1105, 507, 1348, 710]
2025-08-04 17:18:30 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 600: 检测到 3 个对象
2025-08-04 17:18:30 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:18:30 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.924, 位置: [1, 597, 491, 898]
2025-08-04 17:18:30 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:18:30 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.876, 位置: [1169, 224, 1426, 518]
2025-08-04 17:18:30 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:18:30 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.948, 位置: [1104, 507, 1348, 711]
2025-08-04 17:18:30 | INFO     | src.state_machine.action_detector:_log_statistics:397 | 统计信息 - 帧数: 600, 运行时间: 21.4s, FPS: 28.0, 状态: idle, 动作数: 1
2025-08-04 17:18:30 | INFO     | src.state_machine.action_detector:_log_statistics:405 | 检测统计 - 有检测的帧: 600/600 (100.0%), 总检测数: 1833, 手: 33, 蓝色箱子: 600, 上层黄色箱子: 600, 下层黄色箱子: 600
2025-08-04 17:18:30 | INFO     | src.state_machine.action_detector:_log_statistics:415 | 性能统计 - 平均推理时间: 14.7ms, 估计FPS: 68.0, 设备: 0
2025-08-04 17:18:30 | INFO     | src.state_machine.action_detector:_log_statistics:420 | GPU内存 - 已分配: 43.5MB, 已缓存: 90.0MB
2025-08-04 17:18:33 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 700: 检测到 3 个对象
2025-08-04 17:18:33 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:18:33 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.929, 位置: [1, 596, 490, 898]
2025-08-04 17:18:33 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:18:33 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.892, 位置: [1168, 223, 1425, 518]
2025-08-04 17:18:33 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:18:33 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.942, 位置: [1105, 508, 1348, 711]
2025-08-04 17:18:37 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 800: 检测到 3 个对象
2025-08-04 17:18:37 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:18:37 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.921, 位置: [1, 596, 490, 898]
2025-08-04 17:18:37 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:18:37 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.905, 位置: [1168, 223, 1426, 518]
2025-08-04 17:18:37 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:18:37 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.943, 位置: [1106, 508, 1348, 711]
2025-08-04 17:18:40 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 900: 检测到 3 个对象
2025-08-04 17:18:40 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:18:40 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.925, 位置: [1, 597, 490, 898]
2025-08-04 17:18:40 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:18:40 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.905, 位置: [1168, 223, 1426, 518]
2025-08-04 17:18:40 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:18:40 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.928, 位置: [1105, 507, 1348, 711]
2025-08-04 17:18:40 | INFO     | src.state_machine.action_detector:_log_statistics:397 | 统计信息 - 帧数: 900, 运行时间: 31.5s, FPS: 28.6, 状态: idle, 动作数: 1
2025-08-04 17:18:40 | INFO     | src.state_machine.action_detector:_log_statistics:405 | 检测统计 - 有检测的帧: 900/900 (100.0%), 总检测数: 2733, 手: 33, 蓝色箱子: 900, 上层黄色箱子: 900, 下层黄色箱子: 900
2025-08-04 17:18:40 | INFO     | src.state_machine.action_detector:_log_statistics:415 | 性能统计 - 平均推理时间: 14.0ms, 估计FPS: 71.6, 设备: 0
2025-08-04 17:18:40 | INFO     | src.state_machine.action_detector:_log_statistics:420 | GPU内存 - 已分配: 43.5MB, 已缓存: 90.0MB
2025-08-04 17:18:43 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 1000: 检测到 3 个对象
2025-08-04 17:18:43 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:18:43 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.926, 位置: [1, 596, 490, 898]
2025-08-04 17:18:43 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:18:43 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.906, 位置: [1168, 224, 1426, 518]
2025-08-04 17:18:43 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:18:43 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.930, 位置: [1107, 507, 1348, 711]
2025-08-04 17:18:47 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 1100: 检测到 3 个对象
2025-08-04 17:18:47 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:18:47 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.923, 位置: [1, 596, 490, 898]
2025-08-04 17:18:47 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:18:47 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.907, 位置: [1168, 223, 1426, 518]
2025-08-04 17:18:47 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:18:47 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.939, 位置: [1104, 508, 1348, 710]
2025-08-04 17:18:50 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 1200: 检测到 3 个对象
2025-08-04 17:18:50 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:18:50 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.926, 位置: [1, 596, 490, 898]
2025-08-04 17:18:50 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:18:50 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.882, 位置: [1169, 223, 1426, 518]
2025-08-04 17:18:50 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:18:50 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.934, 位置: [1108, 508, 1348, 711]
2025-08-04 17:18:50 | INFO     | src.state_machine.action_detector:_log_statistics:397 | 统计信息 - 帧数: 1200, 运行时间: 41.5s, FPS: 28.9, 状态: idle, 动作数: 1
2025-08-04 17:18:50 | INFO     | src.state_machine.action_detector:_log_statistics:405 | 检测统计 - 有检测的帧: 1200/1200 (100.0%), 总检测数: 3634, 手: 34, 蓝色箱子: 1200, 上层黄色箱子: 1200, 下层黄色箱子: 1200
2025-08-04 17:18:50 | INFO     | src.state_machine.action_detector:_log_statistics:415 | 性能统计 - 平均推理时间: 13.6ms, 估计FPS: 73.4, 设备: 0
2025-08-04 17:18:50 | INFO     | src.state_machine.action_detector:_log_statistics:420 | GPU内存 - 已分配: 43.5MB, 已缓存: 90.0MB
2025-08-04 17:18:54 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 1300: 检测到 3 个对象
2025-08-04 17:18:54 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:18:54 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.924, 位置: [1, 596, 491, 898]
2025-08-04 17:18:54 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:18:54 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.880, 位置: [1168, 223, 1426, 518]
2025-08-04 17:18:54 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:18:54 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.930, 位置: [1107, 508, 1348, 711]
2025-08-04 17:18:57 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 1400: 检测到 3 个对象
2025-08-04 17:18:57 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:18:57 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.919, 位置: [1, 596, 491, 898]
2025-08-04 17:18:57 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:18:57 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.884, 位置: [1168, 223, 1425, 518]
2025-08-04 17:18:57 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:18:57 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.934, 位置: [1106, 508, 1348, 711]
2025-08-04 17:19:00 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 1500: 检测到 4 个对象
2025-08-04 17:19:00 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - hand: 1 个
2025-08-04 17:19:00 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.661, 位置: [896, 479, 1044, 563]
2025-08-04 17:19:00 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:19:00 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.935, 位置: [1, 596, 491, 898]
2025-08-04 17:19:00 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:19:00 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.889, 位置: [1168, 223, 1426, 518]
2025-08-04 17:19:00 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:19:00 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.937, 位置: [1107, 508, 1348, 711]
2025-08-04 17:19:00 | INFO     | src.state_machine.action_detector:_log_statistics:397 | 统计信息 - 帧数: 1500, 运行时间: 51.5s, FPS: 29.1, 状态: idle, 动作数: 1
2025-08-04 17:19:00 | INFO     | src.state_machine.action_detector:_log_statistics:405 | 检测统计 - 有检测的帧: 1500/1500 (100.0%), 总检测数: 4582, 手: 82, 蓝色箱子: 1500, 上层黄色箱子: 1500, 下层黄色箱子: 1500
2025-08-04 17:19:00 | INFO     | src.state_machine.action_detector:_log_statistics:415 | 性能统计 - 平均推理时间: 13.4ms, 估计FPS: 74.4, 设备: 0
2025-08-04 17:19:00 | INFO     | src.state_machine.action_detector:_log_statistics:420 | GPU内存 - 已分配: 43.5MB, 已缓存: 90.0MB
2025-08-04 17:19:04 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 1600: 检测到 3 个对象
2025-08-04 17:19:04 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:19:04 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.928, 位置: [1, 597, 490, 898]
2025-08-04 17:19:04 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:19:04 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.897, 位置: [1169, 223, 1426, 518]
2025-08-04 17:19:04 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:19:04 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.940, 位置: [1107, 507, 1348, 711]
2025-08-04 17:19:04 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与blue_box交互，IoU: 0.121, 距离: 145.2
2025-08-04 17:19:05 | SUCCESS  | src.state_machine.action_state_machine:_handle_detecting_state:155 | 检测到ok_action！第2次动作，持续时间: 0.50秒
2025-08-04 17:19:05 | SUCCESS  | src.state_machine.action_detector:_handle_action_detected:198 | 检测到动作: action_1754299145_2 (类型: ok_action)
2025-08-04 17:19:05 | SUCCESS  | src.utils.video_buffer:save_action_video:117 | 动作视频已保存: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_ok/action_1754299145_2_20250804_171905.mp4 (31帧)
2025-08-04 17:19:05 | SUCCESS  | src.state_machine.action_detector:_handle_action_detected:206 | 动作视频已保存: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_ok/action_1754299145_2_20250804_171905.mp4
2025-08-04 17:19:05 | ERROR    | src.state_machine.action_detector:_save_action_image:246 | 保存动作图像失败: Unknown format code 'd' for object of type 'str'
2025-08-04 17:19:05 | SUCCESS  | __main__:action_callback:132 | 🎯 检测到动作 #action_1754299145_2
2025-08-04 17:19:05 | INFO     | __main__:action_callback:133 |    动作类型: ✅ OK动作 - 产品放入蓝色箱子
2025-08-04 17:19:05 | INFO     | __main__:action_callback:134 |    容器类型: blue_box
2025-08-04 17:19:05 | INFO     | __main__:action_callback:135 |    时间戳: 17:19:05
2025-08-04 17:19:05 | INFO     | __main__:action_callback:139 |    交互时长: 0.50秒
2025-08-04 17:19:05 | INFO     | __main__:action_callback:140 |    IoU: 0.119
2025-08-04 17:19:05 | INFO     | __main__:action_callback:141 |    距离: 63.3px
2025-08-04 17:19:05 | INFO     | __main__:action_callback:142 |    置信度: 0.637
2025-08-04 17:19:05 | INFO     | __main__:action_callback:145 |    保存路径: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_ok/
2025-08-04 17:19:07 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 1700: 检测到 3 个对象
2025-08-04 17:19:07 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:19:07 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.938, 位置: [1, 598, 490, 898]
2025-08-04 17:19:07 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:19:07 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.893, 位置: [1169, 223, 1426, 518]
2025-08-04 17:19:07 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:19:07 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.943, 位置: [1104, 508, 1348, 711]
2025-08-04 17:19:10 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 1800: 检测到 4 个对象
2025-08-04 17:19:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - hand: 1 个
2025-08-04 17:19:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.554, 位置: [880, 456, 1036, 570]
2025-08-04 17:19:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:19:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.934, 位置: [1, 596, 490, 898]
2025-08-04 17:19:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:19:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.888, 位置: [1168, 224, 1426, 518]
2025-08-04 17:19:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:19:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.942, 位置: [1106, 508, 1348, 711]
2025-08-04 17:19:10 | INFO     | src.state_machine.action_detector:_log_statistics:397 | 统计信息 - 帧数: 1800, 运行时间: 61.7s, FPS: 29.2, 状态: idle, 动作数: 2
2025-08-04 17:19:10 | INFO     | src.state_machine.action_detector:_log_statistics:405 | 检测统计 - 有检测的帧: 1800/1800 (100.0%), 总检测数: 5502, 手: 102, 蓝色箱子: 1800, 上层黄色箱子: 1800, 下层黄色箱子: 1800
2025-08-04 17:19:10 | INFO     | src.state_machine.action_detector:_log_statistics:415 | 性能统计 - 平均推理时间: 13.2ms, 估计FPS: 75.7, 设备: 0
2025-08-04 17:19:10 | INFO     | src.state_machine.action_detector:_log_statistics:420 | GPU内存 - 已分配: 43.5MB, 已缓存: 90.0MB
2025-08-04 17:19:14 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 1900: 检测到 3 个对象
2025-08-04 17:19:14 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:19:14 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.930, 位置: [1, 597, 491, 898]
2025-08-04 17:19:14 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:19:14 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.889, 位置: [1165, 223, 1427, 518]
2025-08-04 17:19:14 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:19:14 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.948, 位置: [1104, 508, 1348, 711]
2025-08-04 17:19:17 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 2000: 检测到 3 个对象
2025-08-04 17:19:17 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:19:17 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.935, 位置: [1, 596, 490, 898]
2025-08-04 17:19:17 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:19:17 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.897, 位置: [1169, 223, 1426, 518]
2025-08-04 17:19:17 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:19:17 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.942, 位置: [1105, 507, 1348, 711]
2025-08-04 17:19:18 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与blue_box交互，IoU: 0.104, 距离: 142.1
2025-08-04 17:19:18 | INFO     | src.state_machine.action_state_machine:_handle_detecting_state:130 | 手部与blue_box的交互中断
2025-08-04 17:19:18 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与blue_box交互，IoU: 0.134, 距离: 59.6
2025-08-04 17:19:19 | INFO     | src.state_machine.action_state_machine:_handle_detecting_state:130 | 手部与blue_box的交互中断
2025-08-04 17:19:19 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与blue_box交互，IoU: 0.126, 距离: 63.2
2025-08-04 17:19:19 | INFO     | src.state_machine.action_state_machine:_handle_detecting_state:130 | 手部与blue_box的交互中断
2025-08-04 17:19:20 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 2100: 检测到 3 个对象
2025-08-04 17:19:20 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:19:20 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.935, 位置: [1, 597, 491, 898]
2025-08-04 17:19:20 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:19:20 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.907, 位置: [1168, 223, 1426, 518]
2025-08-04 17:19:20 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:19:20 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.933, 位置: [1107, 507, 1348, 711]
2025-08-04 17:19:20 | INFO     | src.state_machine.action_detector:_log_statistics:397 | 统计信息 - 帧数: 2100, 运行时间: 71.8s, FPS: 29.3, 状态: idle, 动作数: 2
2025-08-04 17:19:20 | INFO     | src.state_machine.action_detector:_log_statistics:405 | 检测统计 - 有检测的帧: 2100/2100 (100.0%), 总检测数: 6436, 手: 136, 蓝色箱子: 2100, 上层黄色箱子: 2100, 下层黄色箱子: 2100
2025-08-04 17:19:20 | INFO     | src.state_machine.action_detector:_log_statistics:415 | 性能统计 - 平均推理时间: 13.1ms, 估计FPS: 76.5, 设备: 0
2025-08-04 17:19:20 | INFO     | src.state_machine.action_detector:_log_statistics:420 | GPU内存 - 已分配: 43.5MB, 已缓存: 90.0MB
2025-08-04 17:19:24 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 2200: 检测到 3 个对象
2025-08-04 17:19:24 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:19:24 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.945, 位置: [1, 596, 490, 898]
2025-08-04 17:19:24 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:19:24 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.900, 位置: [1168, 223, 1426, 518]
2025-08-04 17:19:24 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:19:24 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.951, 位置: [1105, 507, 1348, 710]
2025-08-04 17:19:27 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 2300: 检测到 3 个对象
2025-08-04 17:19:27 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:19:27 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.937, 位置: [0, 587, 492, 898]
2025-08-04 17:19:27 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:19:27 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.898, 位置: [1168, 223, 1426, 518]
2025-08-04 17:19:27 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:19:27 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.931, 位置: [1104, 508, 1348, 711]
2025-08-04 17:19:30 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 2400: 检测到 3 个对象
2025-08-04 17:19:30 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:19:30 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.932, 位置: [1, 597, 490, 898]
2025-08-04 17:19:30 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:19:30 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.880, 位置: [1168, 223, 1426, 518]
2025-08-04 17:19:30 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:19:30 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.935, 位置: [1105, 508, 1348, 711]
2025-08-04 17:19:30 | INFO     | src.state_machine.action_detector:_log_statistics:397 | 统计信息 - 帧数: 2400, 运行时间: 81.8s, FPS: 29.3, 状态: idle, 动作数: 2
2025-08-04 17:19:30 | INFO     | src.state_machine.action_detector:_log_statistics:405 | 检测统计 - 有检测的帧: 2400/2400 (100.0%), 总检测数: 7340, 手: 140, 蓝色箱子: 2400, 上层黄色箱子: 2400, 下层黄色箱子: 2400
2025-08-04 17:19:30 | INFO     | src.state_machine.action_detector:_log_statistics:415 | 性能统计 - 平均推理时间: 13.0ms, 估计FPS: 76.9, 设备: 0
2025-08-04 17:19:30 | INFO     | src.state_machine.action_detector:_log_statistics:420 | GPU内存 - 已分配: 43.5MB, 已缓存: 90.0MB
2025-08-04 17:19:34 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 2500: 检测到 3 个对象
2025-08-04 17:19:34 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:19:34 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.934, 位置: [1, 596, 490, 898]
2025-08-04 17:19:34 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:19:34 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.867, 位置: [1168, 224, 1425, 518]
2025-08-04 17:19:34 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:19:34 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.938, 位置: [1107, 508, 1348, 710]
2025-08-04 17:19:37 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 2600: 检测到 3 个对象
2025-08-04 17:19:37 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:19:37 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.936, 位置: [2, 596, 491, 898]
2025-08-04 17:19:37 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:19:37 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.895, 位置: [1168, 223, 1425, 518]
2025-08-04 17:19:37 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:19:37 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.947, 位置: [1104, 508, 1348, 711]
2025-08-04 17:19:41 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 2700: 检测到 3 个对象
2025-08-04 17:19:41 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:19:41 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.932, 位置: [1, 596, 490, 898]
2025-08-04 17:19:41 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:19:41 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.904, 位置: [1168, 223, 1426, 518]
2025-08-04 17:19:41 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:19:41 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.948, 位置: [1104, 507, 1348, 711]
2025-08-04 17:19:41 | INFO     | src.state_machine.action_detector:_log_statistics:397 | 统计信息 - 帧数: 2700, 运行时间: 91.8s, FPS: 29.4, 状态: idle, 动作数: 2
2025-08-04 17:19:41 | INFO     | src.state_machine.action_detector:_log_statistics:405 | 检测统计 - 有检测的帧: 2700/2700 (100.0%), 总检测数: 8240, 手: 140, 蓝色箱子: 2700, 上层黄色箱子: 2700, 下层黄色箱子: 2700
2025-08-04 17:19:41 | INFO     | src.state_machine.action_detector:_log_statistics:415 | 性能统计 - 平均推理时间: 12.9ms, 估计FPS: 77.6, 设备: 0
2025-08-04 17:19:41 | INFO     | src.state_machine.action_detector:_log_statistics:420 | GPU内存 - 已分配: 43.5MB, 已缓存: 90.0MB
2025-08-04 17:19:44 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 2800: 检测到 3 个对象
2025-08-04 17:19:44 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:19:44 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.931, 位置: [1, 597, 491, 898]
2025-08-04 17:19:44 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:19:44 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.871, 位置: [1168, 224, 1426, 518]
2025-08-04 17:19:44 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:19:44 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.951, 位置: [1105, 508, 1348, 711]
2025-08-04 17:19:47 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 2900: 检测到 3 个对象
2025-08-04 17:19:47 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:19:47 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.937, 位置: [1, 596, 490, 898]
2025-08-04 17:19:47 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:19:47 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.888, 位置: [1168, 224, 1426, 518]
2025-08-04 17:19:47 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:19:47 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.956, 位置: [1107, 508, 1348, 711]
2025-08-04 17:19:51 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 3000: 检测到 3 个对象
2025-08-04 17:19:51 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:19:51 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.932, 位置: [1, 597, 490, 898]
2025-08-04 17:19:51 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:19:51 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.894, 位置: [1168, 223, 1426, 518]
2025-08-04 17:19:51 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:19:51 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.947, 位置: [1104, 508, 1348, 711]
2025-08-04 17:19:51 | INFO     | src.state_machine.action_detector:_log_statistics:397 | 统计信息 - 帧数: 3000, 运行时间: 101.9s, FPS: 29.4, 状态: idle, 动作数: 2
2025-08-04 17:19:51 | INFO     | src.state_machine.action_detector:_log_statistics:405 | 检测统计 - 有检测的帧: 3000/3000 (100.0%), 总检测数: 9140, 手: 140, 蓝色箱子: 3000, 上层黄色箱子: 3000, 下层黄色箱子: 3000
2025-08-04 17:19:51 | INFO     | src.state_machine.action_detector:_log_statistics:415 | 性能统计 - 平均推理时间: 12.8ms, 估计FPS: 78.2, 设备: 0
2025-08-04 17:19:51 | INFO     | src.state_machine.action_detector:_log_statistics:420 | GPU内存 - 已分配: 43.5MB, 已缓存: 90.0MB
2025-08-04 17:19:54 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 3100: 检测到 3 个对象
2025-08-04 17:19:54 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:19:54 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.938, 位置: [1, 597, 490, 898]
2025-08-04 17:19:54 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:19:54 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.903, 位置: [1168, 223, 1426, 518]
2025-08-04 17:19:54 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:19:54 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.950, 位置: [1105, 508, 1348, 711]
2025-08-04 17:19:57 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 3200: 检测到 3 个对象
2025-08-04 17:19:57 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:19:57 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.932, 位置: [1, 596, 491, 898]
2025-08-04 17:19:57 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:19:57 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.893, 位置: [1168, 224, 1425, 518]
2025-08-04 17:19:57 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:19:57 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.957, 位置: [1105, 508, 1348, 711]
2025-08-04 17:20:01 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 3300: 检测到 3 个对象
2025-08-04 17:20:01 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:20:01 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.934, 位置: [1, 596, 491, 898]
2025-08-04 17:20:01 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:20:01 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.891, 位置: [1168, 223, 1425, 518]
2025-08-04 17:20:01 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:20:01 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.954, 位置: [1106, 508, 1348, 711]
2025-08-04 17:20:01 | INFO     | src.state_machine.action_detector:_log_statistics:397 | 统计信息 - 帧数: 3300, 运行时间: 111.9s, FPS: 29.5, 状态: idle, 动作数: 2
2025-08-04 17:20:01 | INFO     | src.state_machine.action_detector:_log_statistics:405 | 检测统计 - 有检测的帧: 3300/3300 (100.0%), 总检测数: 10040, 手: 140, 蓝色箱子: 3300, 上层黄色箱子: 3300, 下层黄色箱子: 3300
2025-08-04 17:20:01 | INFO     | src.state_machine.action_detector:_log_statistics:415 | 性能统计 - 平均推理时间: 12.7ms, 估计FPS: 78.9, 设备: 0
2025-08-04 17:20:01 | INFO     | src.state_machine.action_detector:_log_statistics:420 | GPU内存 - 已分配: 43.5MB, 已缓存: 90.0MB
2025-08-04 17:20:04 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 3400: 检测到 3 个对象
2025-08-04 17:20:04 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:20:04 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.929, 位置: [1, 597, 490, 898]
2025-08-04 17:20:04 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:20:04 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.888, 位置: [1168, 223, 1426, 518]
2025-08-04 17:20:04 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:20:04 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.944, 位置: [1105, 508, 1348, 712]
2025-08-04 17:20:07 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 3500: 检测到 3 个对象
2025-08-04 17:20:07 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:20:07 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.928, 位置: [1, 597, 491, 898]
2025-08-04 17:20:07 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:20:07 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.894, 位置: [1168, 223, 1426, 518]
2025-08-04 17:20:07 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:20:07 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.949, 位置: [1107, 508, 1348, 711]
2025-08-04 17:20:11 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 3600: 检测到 3 个对象
2025-08-04 17:20:11 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:20:11 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.937, 位置: [1, 596, 490, 898]
2025-08-04 17:20:11 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:20:11 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.886, 位置: [1168, 224, 1426, 518]
2025-08-04 17:20:11 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:20:11 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.951, 位置: [1107, 508, 1348, 712]
2025-08-04 17:20:11 | INFO     | src.state_machine.action_detector:_log_statistics:397 | 统计信息 - 帧数: 3600, 运行时间: 121.9s, FPS: 29.5, 状态: idle, 动作数: 2
2025-08-04 17:20:11 | INFO     | src.state_machine.action_detector:_log_statistics:405 | 检测统计 - 有检测的帧: 3600/3600 (100.0%), 总检测数: 10940, 手: 140, 蓝色箱子: 3600, 上层黄色箱子: 3600, 下层黄色箱子: 3600
2025-08-04 17:20:11 | INFO     | src.state_machine.action_detector:_log_statistics:415 | 性能统计 - 平均推理时间: 12.4ms, 估计FPS: 80.4, 设备: 0
2025-08-04 17:20:11 | INFO     | src.state_machine.action_detector:_log_statistics:420 | GPU内存 - 已分配: 43.5MB, 已缓存: 90.0MB
2025-08-04 17:20:14 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 3700: 检测到 3 个对象
2025-08-04 17:20:14 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:20:14 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.937, 位置: [0, 588, 491, 898]
2025-08-04 17:20:14 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:20:14 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.886, 位置: [1168, 223, 1425, 518]
2025-08-04 17:20:14 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:20:14 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.943, 位置: [1106, 508, 1348, 711]
2025-08-04 17:20:17 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 3800: 检测到 3 个对象
2025-08-04 17:20:17 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:20:17 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.931, 位置: [0, 586, 491, 898]
2025-08-04 17:20:17 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:20:17 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.895, 位置: [1168, 222, 1426, 518]
2025-08-04 17:20:17 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:20:17 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.921, 位置: [1104, 507, 1348, 710]
2025-08-04 17:20:17 | INFO     | src.state_machine.action_detector:run:186 | 检测到键盘中断，正在退出...
2025-08-04 17:20:17 | INFO     | src.detection.video_capture:release:203 | 视频捕获资源已释放
2025-08-04 17:20:17 | INFO     | src.state_machine.action_detector:_cleanup:435 | 检测完成 - 总帧数: 3801, 总运行时间: 128.6s, 平均FPS: 29.5, 检测到的动作数: 2
2025-08-04 17:20:17 | INFO     | __main__:main:261 | ============================================================
2025-08-04 17:20:17 | INFO     | __main__:main:262 | 👋 程序结束
2025-08-04 17:20:17 | INFO     | __main__:main:263 | ============================================================
2025-08-04 17:20:30 | INFO     | src.utils.logger:setup_logger:55 | 日志系统初始化完成，级别: INFO
2025-08-04 17:20:30 | INFO     | src.utils.logger:setup_logger:57 | 日志文件: logs/action_detection.log
2025-08-04 17:20:30 | INFO     | __main__:main:170 | ============================================================
2025-08-04 17:20:30 | INFO     | __main__:main:171 | 🚀 YOLOv8实时动作检测系统启动
2025-08-04 17:20:30 | INFO     | __main__:main:172 | ============================================================
2025-08-04 17:20:30 | INFO     | __main__:main:178 | 使用命令行指定的视频文件: /home/<USER>/wolong_ws/yolo_detect_ws/data/test_videos/test_video_long.mp4
2025-08-04 17:20:30 | INFO     | __main__:main:189 | 📹 视频源: /home/<USER>/wolong_ws/yolo_detect_ws/data/test_videos/test_video_long.mp4
2025-08-04 17:20:30 | INFO     | __main__:main:190 | 🤖 模型路径: yolo_training/yolov8n_custom2/weights/best.pt
2025-08-04 17:20:30 | INFO     | __main__:main:191 | 📁 输出目录: output/detected_actions
2025-08-04 17:20:30 | INFO     | __main__:main:192 | 🎯 置信度阈值: 0.5
2025-08-04 17:20:30 | INFO     | __main__:main:193 | 📊 交互阈值 - IoU: 0.1, 距离: 50px
2025-08-04 17:20:30 | INFO     | __main__:main:194 | ⏰ 冷却时间: 3.0秒, 动作持续时间: 0.5秒
2025-08-04 17:20:30 | INFO     | src.detection.detector:_get_device:55 | torch.cuda.is_available(): True
2025-08-04 17:20:30 | INFO     | src.detection.detector:_get_device:56 | torch.cuda.device_count(): 1
2025-08-04 17:20:30 | INFO     | src.detection.detector:_get_device:62 | 检测到GPU: NVIDIA GeForce RTX 4060 Laptop GPU (7.6GB)
2025-08-04 17:20:30 | INFO     | src.detection.detector:_get_device:66 | 使用GPU设备: cuda:0
2025-08-04 17:20:30 | INFO     | src.detection.detector:_get_device:88 | 使用设备: 0
2025-08-04 17:20:30 | INFO     | src.detection.detector:_get_device:97 | GPU缓存已清理
2025-08-04 17:20:31 | INFO     | src.detection.detector:_get_device:101 | GPU内存分配策略已设置
2025-08-04 17:20:31 | INFO     | src.detection.detector:_load_model:117 | 加载模型: yolo_training/yolov8n_custom2/weights/best.pt
2025-08-04 17:20:31 | INFO     | src.detection.detector:_load_model:123 | 将模型移动到GPU设备: 0
2025-08-04 17:20:31 | INFO     | src.detection.detector:_load_model:129 | 模型已成功移动到GPU
2025-08-04 17:20:31 | INFO     | src.detection.detector:_load_model:138 | 检测类别: {0: 'blue_box', 1: 'upper_yellow_box', 2: 'lower_yellow_box', 3: 'hand'}
2025-08-04 17:20:31 | INFO     | src.detection.video_capture:_initialize_capture:52 | 初始化视频文件: /home/<USER>/wolong_ws/yolo_detect_ws/data/test_videos/test_video_long.mp4
2025-08-04 17:20:31 | INFO     | src.detection.video_capture:_log_video_info:74 | 视频信息 - 分辨率: 1442x898, FPS: 25.00
2025-08-04 17:20:31 | INFO     | src.detection.video_capture:_log_video_info:79 | 视频时长: 304.00秒, 总帧数: 7600
2025-08-04 17:20:31 | INFO     | src.state_machine.action_state_machine:__init__:73 | 状态机初始化完成，初始状态: idle
2025-08-04 17:20:31 | INFO     | src.utils.video_buffer:__init__:33 | 视频缓存器初始化完成，缓存大小: 31帧，帧率: 30.0fps
2025-08-04 17:20:31 | INFO     | src.utils.video_buffer:__init__:168 | 确保目录存在: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_ok
2025-08-04 17:20:31 | INFO     | src.utils.video_buffer:__init__:168 | 确保目录存在: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_nok_electric
2025-08-04 17:20:31 | INFO     | src.utils.video_buffer:__init__:168 | 确保目录存在: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_nok_appearence
2025-08-04 17:20:31 | INFO     | src.state_machine.action_detector:__init__:96 | 视频保存功能已启用，缓存大小: 31帧
2025-08-04 17:20:31 | INFO     | src.state_machine.action_detector:__init__:120 | 动作检测器初始化完成
2025-08-04 17:20:31 | INFO     | __main__:main:226 | 🖥️  推理设备: 0
2025-08-04 17:20:31 | INFO     | __main__:main:237 | 💡 控制提示:
2025-08-04 17:20:31 | INFO     | __main__:main:238 |    按 'q' 或 ESC 退出程序
2025-08-04 17:20:31 | INFO     | __main__:main:239 |    按 'r' 重置状态机
2025-08-04 17:20:31 | INFO     | __main__:main:240 |    按 'd' 切换调试模式
2025-08-04 17:20:31 | INFO     | __main__:main:242 | 🔍 开始检测...
2025-08-04 17:20:31 | INFO     | __main__:main:243 | ------------------------------------------------------------
2025-08-04 17:20:31 | INFO     | src.state_machine.action_detector:run:139 | 开始运行动作检测
2025-08-04 17:20:32 | INFO     | src.detection.detector:detect:223 | 检测调试 #1: 原始检测数=3, 过滤后检测数=3, 置信度阈值=0.5
2025-08-04 17:20:32 | INFO     | src.detection.detector:detect:225 | 推理时间: 1136.6ms, 平均: 1136.6ms
2025-08-04 17:20:32 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 17:20:32 | INFO     | src.detection.detector:detect:233 | 原始类别: [1.0, 2.0, 3.0]
2025-08-04 17:20:32 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.8869718313217163, 0.6533556580543518, 0.6350385546684265]
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 1: 检测到 3 个对象
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - hand: 1 个
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.635, 位置: [76, 638, 285, 791]
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.887, 位置: [1142, 235, 1424, 524]
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.653, 位置: [1099, 508, 1340, 710]
2025-08-04 17:20:32 | INFO     | src.detection.detector:detect:223 | 检测调试 #2: 原始检测数=3, 过滤后检测数=3, 置信度阈值=0.5
2025-08-04 17:20:32 | INFO     | src.detection.detector:detect:225 | 推理时间: 7.1ms, 平均: 571.8ms
2025-08-04 17:20:32 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 17:20:32 | INFO     | src.detection.detector:detect:233 | 原始类别: [1.0, 3.0, 2.0]
2025-08-04 17:20:32 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.8979863524436951, 0.7020838260650635, 0.6313614845275879]
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 2: 检测到 3 个对象
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - hand: 1 个
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.702, 位置: [74, 646, 278, 791]
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.898, 位置: [1141, 235, 1424, 523]
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.631, 位置: [1099, 508, 1339, 710]
2025-08-04 17:20:32 | INFO     | src.detection.detector:detect:223 | 检测调试 #3: 原始检测数=3, 过滤后检测数=3, 置信度阈值=0.5
2025-08-04 17:20:32 | INFO     | src.detection.detector:detect:225 | 推理时间: 6.1ms, 平均: 383.2ms
2025-08-04 17:20:32 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 17:20:32 | INFO     | src.detection.detector:detect:233 | 原始类别: [1.0, 3.0, 2.0]
2025-08-04 17:20:32 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.9000638723373413, 0.6370683908462524, 0.6329067349433899]
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 3: 检测到 3 个对象
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - hand: 1 个
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.637, 位置: [74, 647, 275, 792]
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.900, 位置: [1141, 235, 1424, 523]
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.633, 位置: [1099, 508, 1339, 710]
2025-08-04 17:20:32 | INFO     | src.detection.detector:detect:223 | 检测调试 #4: 原始检测数=3, 过滤后检测数=3, 置信度阈值=0.5
2025-08-04 17:20:32 | INFO     | src.detection.detector:detect:225 | 推理时间: 6.2ms, 平均: 289.0ms
2025-08-04 17:20:32 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 17:20:32 | INFO     | src.detection.detector:detect:233 | 原始类别: [1.0, 3.0, 2.0]
2025-08-04 17:20:32 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.9004483819007874, 0.6841287612915039, 0.6248301863670349]
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 4: 检测到 3 个对象
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - hand: 1 个
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.684, 位置: [82, 650, 269, 794]
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.900, 位置: [1141, 235, 1424, 523]
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.625, 位置: [1099, 508, 1340, 710]
2025-08-04 17:20:32 | INFO     | src.detection.detector:detect:223 | 检测调试 #5: 原始检测数=3, 过滤后检测数=3, 置信度阈值=0.5
2025-08-04 17:20:32 | INFO     | src.detection.detector:detect:225 | 推理时间: 6.0ms, 平均: 232.4ms
2025-08-04 17:20:32 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 17:20:32 | INFO     | src.detection.detector:detect:233 | 原始类别: [1.0, 3.0, 2.0]
2025-08-04 17:20:32 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.8988938927650452, 0.6888245940208435, 0.6244968771934509]
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 5: 检测到 3 个对象
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - hand: 1 个
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.689, 位置: [87, 647, 264, 793]
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.899, 位置: [1141, 235, 1424, 523]
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.624, 位置: [1099, 508, 1340, 710]
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 6: 检测到 3 个对象
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - hand: 1 个
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.718, 位置: [85, 643, 264, 792]
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.898, 位置: [1141, 235, 1424, 523]
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.629, 位置: [1099, 508, 1340, 710]
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 7: 检测到 3 个对象
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - hand: 1 个
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.802, 位置: [89, 642, 267, 791]
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.899, 位置: [1141, 235, 1425, 523]
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.634, 位置: [1099, 508, 1340, 710]
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 8: 检测到 3 个对象
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - hand: 1 个
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.847, 位置: [91, 636, 272, 790]
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.903, 位置: [1141, 235, 1424, 523]
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.630, 位置: [1099, 508, 1340, 710]
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 9: 检测到 3 个对象
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - hand: 1 个
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.833, 位置: [86, 633, 278, 785]
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.904, 位置: [1141, 235, 1424, 523]
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.625, 位置: [1099, 508, 1340, 710]
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 10: 检测到 3 个对象
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - hand: 1 个
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.704, 位置: [80, 594, 284, 776]
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.903, 位置: [1141, 235, 1424, 523]
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:20:32 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.639, 位置: [1099, 508, 1340, 710]
2025-08-04 17:20:35 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 100: 检测到 2 个对象
2025-08-04 17:20:35 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:20:35 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.899, 位置: [1142, 235, 1425, 524]
2025-08-04 17:20:35 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:20:35 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.671, 位置: [1099, 508, 1342, 710]
2025-08-04 17:20:38 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 200: 检测到 2 个对象
2025-08-04 17:20:38 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:20:38 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.900, 位置: [1143, 235, 1425, 524]
2025-08-04 17:20:38 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:20:38 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.670, 位置: [1099, 508, 1342, 711]
2025-08-04 17:20:42 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 300: 检测到 2 个对象
2025-08-04 17:20:42 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:20:42 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.884, 位置: [1143, 235, 1424, 523]
2025-08-04 17:20:42 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:20:42 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.660, 位置: [1099, 509, 1342, 710]
2025-08-04 17:20:42 | INFO     | src.state_machine.action_detector:_log_statistics:397 | 统计信息 - 帧数: 300, 运行时间: 11.2s, FPS: 26.7, 状态: idle, 动作数: 0
2025-08-04 17:20:42 | INFO     | src.state_machine.action_detector:_log_statistics:405 | 检测统计 - 有检测的帧: 300/300 (100.0%), 总检测数: 627, 手: 27, 蓝色箱子: 0, 上层黄色箱子: 300, 下层黄色箱子: 300
2025-08-04 17:20:42 | INFO     | src.state_machine.action_detector:_log_statistics:415 | 性能统计 - 平均推理时间: 16.4ms, 估计FPS: 61.1, 设备: 0
2025-08-04 17:20:42 | INFO     | src.state_machine.action_detector:_log_statistics:420 | GPU内存 - 已分配: 43.5MB, 已缓存: 90.0MB
2025-08-04 17:20:45 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 400: 检测到 2 个对象
2025-08-04 17:20:45 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:20:45 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.883, 位置: [1143, 233, 1425, 524]
2025-08-04 17:20:45 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:20:45 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.620, 位置: [1099, 509, 1343, 710]
2025-08-04 17:20:49 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 500: 检测到 2 个对象
2025-08-04 17:20:49 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:20:49 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.877, 位置: [1144, 233, 1425, 524]
2025-08-04 17:20:49 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:20:49 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.605, 位置: [1099, 508, 1343, 710]
2025-08-04 17:20:52 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 600: 检测到 2 个对象
2025-08-04 17:20:52 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:20:52 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.911, 位置: [1141, 235, 1425, 524]
2025-08-04 17:20:52 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:20:52 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.650, 位置: [1099, 509, 1343, 710]
2025-08-04 17:20:52 | INFO     | src.state_machine.action_detector:_log_statistics:397 | 统计信息 - 帧数: 600, 运行时间: 21.3s, FPS: 28.2, 状态: idle, 动作数: 0
2025-08-04 17:20:52 | INFO     | src.state_machine.action_detector:_log_statistics:405 | 检测统计 - 有检测的帧: 600/600 (100.0%), 总检测数: 1269, 手: 69, 蓝色箱子: 0, 上层黄色箱子: 600, 下层黄色箱子: 600
2025-08-04 17:20:52 | INFO     | src.state_machine.action_detector:_log_statistics:415 | 性能统计 - 平均推理时间: 14.7ms, 估计FPS: 68.0, 设备: 0
2025-08-04 17:20:52 | INFO     | src.state_machine.action_detector:_log_statistics:420 | GPU内存 - 已分配: 43.5MB, 已缓存: 90.0MB
2025-08-04 17:20:52 | INFO     | src.state_machine.action_detector:run:186 | 检测到键盘中断，正在退出...
2025-08-04 17:20:52 | INFO     | src.detection.video_capture:release:203 | 视频捕获资源已释放
2025-08-04 17:20:52 | INFO     | src.state_machine.action_detector:_cleanup:435 | 检测完成 - 总帧数: 605, 总运行时间: 21.4s, 平均FPS: 28.2, 检测到的动作数: 0
2025-08-04 17:20:52 | INFO     | __main__:main:261 | ============================================================
2025-08-04 17:20:52 | INFO     | __main__:main:262 | 👋 程序结束
2025-08-04 17:20:52 | INFO     | __main__:main:263 | ============================================================
2025-08-04 17:22:09 | INFO     | src.utils.logger:setup_logger:55 | 日志系统初始化完成，级别: INFO
2025-08-04 17:22:09 | INFO     | src.utils.logger:setup_logger:57 | 日志文件: logs/action_detection.log
2025-08-04 17:22:09 | INFO     | __main__:main:170 | ============================================================
2025-08-04 17:22:09 | INFO     | __main__:main:171 | 🚀 YOLOv8实时动作检测系统启动
2025-08-04 17:22:09 | INFO     | __main__:main:172 | ============================================================
2025-08-04 17:22:09 | INFO     | __main__:main:178 | 使用命令行指定的视频文件: /home/<USER>/wolong_ws/yolo_detect_ws/data/test_videos/test_video_right_place.mp4
2025-08-04 17:22:09 | INFO     | __main__:main:189 | 📹 视频源: /home/<USER>/wolong_ws/yolo_detect_ws/data/test_videos/test_video_right_place.mp4
2025-08-04 17:22:09 | INFO     | __main__:main:190 | 🤖 模型路径: yolo_training/yolov8n_custom2/weights/best.pt
2025-08-04 17:22:09 | INFO     | __main__:main:191 | 📁 输出目录: output/detected_actions
2025-08-04 17:22:09 | INFO     | __main__:main:192 | 🎯 置信度阈值: 0.5
2025-08-04 17:22:09 | INFO     | __main__:main:193 | 📊 交互阈值 - IoU: 0.1, 距离: 50px
2025-08-04 17:22:09 | INFO     | __main__:main:194 | ⏰ 冷却时间: 3.0秒, 动作持续时间: 0.5秒
2025-08-04 17:22:09 | INFO     | src.detection.detector:_get_device:55 | torch.cuda.is_available(): True
2025-08-04 17:22:09 | INFO     | src.detection.detector:_get_device:56 | torch.cuda.device_count(): 1
2025-08-04 17:22:09 | INFO     | src.detection.detector:_get_device:62 | 检测到GPU: NVIDIA GeForce RTX 4060 Laptop GPU (7.6GB)
2025-08-04 17:22:09 | INFO     | src.detection.detector:_get_device:66 | 使用GPU设备: cuda:0
2025-08-04 17:22:09 | INFO     | src.detection.detector:_get_device:88 | 使用设备: 0
2025-08-04 17:22:09 | INFO     | src.detection.detector:_get_device:97 | GPU缓存已清理
2025-08-04 17:22:09 | INFO     | src.detection.detector:_get_device:101 | GPU内存分配策略已设置
2025-08-04 17:22:09 | INFO     | src.detection.detector:_load_model:117 | 加载模型: yolo_training/yolov8n_custom2/weights/best.pt
2025-08-04 17:22:09 | INFO     | src.detection.detector:_load_model:123 | 将模型移动到GPU设备: 0
2025-08-04 17:22:09 | INFO     | src.detection.detector:_load_model:129 | 模型已成功移动到GPU
2025-08-04 17:22:09 | INFO     | src.detection.detector:_load_model:138 | 检测类别: {0: 'blue_box', 1: 'upper_yellow_box', 2: 'lower_yellow_box', 3: 'hand'}
2025-08-04 17:22:09 | INFO     | src.detection.video_capture:_initialize_capture:52 | 初始化视频文件: /home/<USER>/wolong_ws/yolo_detect_ws/data/test_videos/test_video_right_place.mp4
2025-08-04 17:22:09 | INFO     | src.detection.video_capture:_log_video_info:74 | 视频信息 - 分辨率: 1442x898, FPS: 25.00
2025-08-04 17:22:09 | INFO     | src.detection.video_capture:_log_video_info:79 | 视频时长: 300.00秒, 总帧数: 7500
2025-08-04 17:22:09 | INFO     | src.state_machine.action_state_machine:__init__:73 | 状态机初始化完成，初始状态: idle
2025-08-04 17:22:09 | INFO     | src.utils.video_buffer:__init__:33 | 视频缓存器初始化完成，缓存大小: 31帧，帧率: 30.0fps
2025-08-04 17:22:09 | INFO     | src.utils.video_buffer:__init__:168 | 确保目录存在: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_ok
2025-08-04 17:22:09 | INFO     | src.utils.video_buffer:__init__:168 | 确保目录存在: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_nok_electric
2025-08-04 17:22:09 | INFO     | src.utils.video_buffer:__init__:168 | 确保目录存在: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_nok_appearence
2025-08-04 17:22:09 | INFO     | src.state_machine.action_detector:__init__:96 | 视频保存功能已启用，缓存大小: 31帧
2025-08-04 17:22:09 | INFO     | src.state_machine.action_detector:__init__:120 | 动作检测器初始化完成
2025-08-04 17:22:09 | INFO     | __main__:main:226 | 🖥️  推理设备: 0
2025-08-04 17:22:09 | INFO     | __main__:main:237 | 💡 控制提示:
2025-08-04 17:22:09 | INFO     | __main__:main:238 |    按 'q' 或 ESC 退出程序
2025-08-04 17:22:09 | INFO     | __main__:main:239 |    按 'r' 重置状态机
2025-08-04 17:22:09 | INFO     | __main__:main:240 |    按 'd' 切换调试模式
2025-08-04 17:22:09 | INFO     | __main__:main:242 | 🔍 开始检测...
2025-08-04 17:22:09 | INFO     | __main__:main:243 | ------------------------------------------------------------
2025-08-04 17:22:09 | INFO     | src.state_machine.action_detector:run:139 | 开始运行动作检测
2025-08-04 17:22:10 | INFO     | src.detection.detector:detect:223 | 检测调试 #1: 原始检测数=3, 过滤后检测数=3, 置信度阈值=0.5
2025-08-04 17:22:10 | INFO     | src.detection.detector:detect:225 | 推理时间: 1132.1ms, 平均: 1132.1ms
2025-08-04 17:22:10 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 17:22:10 | INFO     | src.detection.detector:detect:233 | 原始类别: [2.0, 0.0, 1.0]
2025-08-04 17:22:10 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.9461163282394409, 0.9411600232124329, 0.9028144478797913]
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 1: 检测到 3 个对象
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.941, 位置: [2, 582, 494, 898]
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.903, 位置: [1168, 223, 1426, 518]
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.946, 位置: [1105, 508, 1348, 711]
2025-08-04 17:22:10 | INFO     | src.detection.detector:detect:223 | 检测调试 #2: 原始检测数=3, 过滤后检测数=3, 置信度阈值=0.5
2025-08-04 17:22:10 | INFO     | src.detection.detector:detect:225 | 推理时间: 6.8ms, 平均: 569.5ms
2025-08-04 17:22:10 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 17:22:10 | INFO     | src.detection.detector:detect:233 | 原始类别: [2.0, 0.0, 1.0]
2025-08-04 17:22:10 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.946992039680481, 0.9393644332885742, 0.9069550633430481]
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 2: 检测到 3 个对象
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.939, 位置: [1, 582, 494, 898]
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.907, 位置: [1168, 223, 1426, 518]
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.947, 位置: [1106, 509, 1348, 712]
2025-08-04 17:22:10 | INFO     | src.detection.detector:detect:223 | 检测调试 #3: 原始检测数=3, 过滤后检测数=3, 置信度阈值=0.5
2025-08-04 17:22:10 | INFO     | src.detection.detector:detect:225 | 推理时间: 6.3ms, 平均: 381.8ms
2025-08-04 17:22:10 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 17:22:10 | INFO     | src.detection.detector:detect:233 | 原始类别: [2.0, 0.0, 1.0]
2025-08-04 17:22:10 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.9462010860443115, 0.9428465962409973, 0.9086053371429443]
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 3: 检测到 3 个对象
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.943, 位置: [1, 582, 493, 898]
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.909, 位置: [1168, 223, 1426, 518]
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.946, 位置: [1105, 509, 1348, 712]
2025-08-04 17:22:10 | INFO     | src.detection.detector:detect:223 | 检测调试 #4: 原始检测数=3, 过滤后检测数=3, 置信度阈值=0.5
2025-08-04 17:22:10 | INFO     | src.detection.detector:detect:225 | 推理时间: 6.4ms, 平均: 287.9ms
2025-08-04 17:22:10 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 17:22:10 | INFO     | src.detection.detector:detect:233 | 原始类别: [0.0, 2.0, 1.0]
2025-08-04 17:22:10 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.9456442594528198, 0.9455066919326782, 0.907799243927002]
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 4: 检测到 3 个对象
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.946, 位置: [1, 582, 493, 898]
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.908, 位置: [1168, 223, 1426, 518]
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.946, 位置: [1105, 509, 1348, 712]
2025-08-04 17:22:10 | INFO     | src.detection.detector:detect:223 | 检测调试 #5: 原始检测数=3, 过滤后检测数=3, 置信度阈值=0.5
2025-08-04 17:22:10 | INFO     | src.detection.detector:detect:225 | 推理时间: 6.8ms, 平均: 231.7ms
2025-08-04 17:22:10 | INFO     | src.detection.detector:detect:230 | GPU内存使用: 43.5MB
2025-08-04 17:22:10 | INFO     | src.detection.detector:detect:233 | 原始类别: [2.0, 0.0, 1.0]
2025-08-04 17:22:10 | INFO     | src.detection.detector:detect:234 | 原始置信度: [0.9467247724533081, 0.9465779662132263, 0.9087066054344177]
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 5: 检测到 3 个对象
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.947, 位置: [1, 582, 494, 898]
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.909, 位置: [1168, 223, 1426, 518]
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.947, 位置: [1105, 509, 1348, 712]
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 6: 检测到 3 个对象
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.948, 位置: [1, 582, 494, 898]
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.908, 位置: [1168, 223, 1426, 518]
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.947, 位置: [1105, 509, 1348, 712]
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 7: 检测到 3 个对象
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.949, 位置: [1, 582, 493, 898]
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.907, 位置: [1168, 223, 1426, 518]
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.947, 位置: [1105, 509, 1348, 712]
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 8: 检测到 3 个对象
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.949, 位置: [1, 582, 494, 898]
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.907, 位置: [1168, 223, 1426, 518]
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.947, 位置: [1106, 509, 1348, 712]
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 9: 检测到 3 个对象
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.948, 位置: [1, 582, 494, 898]
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.908, 位置: [1168, 223, 1426, 518]
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.947, 位置: [1106, 509, 1348, 712]
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 10: 检测到 3 个对象
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.948, 位置: [1, 582, 494, 898]
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.909, 位置: [1168, 223, 1426, 518]
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:22:10 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.946, 位置: [1105, 509, 1348, 712]
2025-08-04 17:22:13 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 100: 检测到 3 个对象
2025-08-04 17:22:13 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:22:13 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.927, 位置: [1, 582, 492, 898]
2025-08-04 17:22:13 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:22:13 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.906, 位置: [1169, 223, 1426, 518]
2025-08-04 17:22:13 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:22:13 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.908, 位置: [1106, 508, 1348, 711]
2025-08-04 17:22:17 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 200: 检测到 3 个对象
2025-08-04 17:22:17 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:22:17 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.931, 位置: [1, 584, 492, 898]
2025-08-04 17:22:17 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:22:17 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.890, 位置: [1168, 223, 1426, 518]
2025-08-04 17:22:17 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:22:17 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.945, 位置: [1104, 507, 1348, 711]
2025-08-04 17:22:20 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 300: 检测到 3 个对象
2025-08-04 17:22:20 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:22:20 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.922, 位置: [1, 584, 493, 898]
2025-08-04 17:22:20 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:22:20 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.909, 位置: [1168, 223, 1426, 518]
2025-08-04 17:22:20 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:22:20 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.928, 位置: [1105, 508, 1348, 711]
2025-08-04 17:22:20 | INFO     | src.state_machine.action_detector:_log_statistics:397 | 统计信息 - 帧数: 300, 运行时间: 11.2s, FPS: 26.8, 状态: idle, 动作数: 0
2025-08-04 17:22:20 | INFO     | src.state_machine.action_detector:_log_statistics:405 | 检测统计 - 有检测的帧: 300/300 (100.0%), 总检测数: 907, 手: 7, 蓝色箱子: 300, 上层黄色箱子: 300, 下层黄色箱子: 300
2025-08-04 17:22:20 | INFO     | src.state_machine.action_detector:_log_statistics:415 | 性能统计 - 平均推理时间: 16.3ms, 估计FPS: 61.4, 设备: 0
2025-08-04 17:22:20 | INFO     | src.state_machine.action_detector:_log_statistics:420 | GPU内存 - 已分配: 43.5MB, 已缓存: 90.0MB
2025-08-04 17:22:23 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 400: 检测到 3 个对象
2025-08-04 17:22:23 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:22:23 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.938, 位置: [0, 582, 491, 898]
2025-08-04 17:22:23 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:22:23 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.901, 位置: [1168, 223, 1426, 518]
2025-08-04 17:22:23 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:22:23 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.947, 位置: [1105, 508, 1348, 711]
2025-08-04 17:22:26 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与blue_box交互，IoU: 0.102, 距离: 157.8
2025-08-04 17:22:26 | SUCCESS  | src.state_machine.action_state_machine:_handle_detecting_state:155 | 检测到ok_action！第1次动作，持续时间: 0.50秒
2025-08-04 17:22:26 | SUCCESS  | src.state_machine.action_detector:_handle_action_detected:198 | 检测到动作: action_1754299346_1 (类型: ok_action)
2025-08-04 17:22:27 | SUCCESS  | src.utils.video_buffer:save_action_video:117 | 动作视频已保存: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_ok/action_1754299346_1_20250804_172226.mp4 (31帧)
2025-08-04 17:22:27 | SUCCESS  | src.state_machine.action_detector:_handle_action_detected:206 | 动作视频已保存: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_ok/action_1754299346_1_20250804_172226.mp4
2025-08-04 17:22:27 | INFO     | src.state_machine.action_detector:_save_action_image:243 | 动作图像已保存: output/detected_actions/action_1754299346_1_20250804_172226.jpg
2025-08-04 17:22:27 | SUCCESS  | __main__:action_callback:132 | 🎯 检测到动作 #action_1754299346_1
2025-08-04 17:22:27 | INFO     | __main__:action_callback:133 |    动作类型: ✅ OK动作 - 产品放入蓝色箱子
2025-08-04 17:22:27 | INFO     | __main__:action_callback:134 |    容器类型: blue_box
2025-08-04 17:22:27 | INFO     | __main__:action_callback:135 |    时间戳: 17:22:26
2025-08-04 17:22:27 | INFO     | __main__:action_callback:139 |    交互时长: 0.50秒
2025-08-04 17:22:27 | INFO     | __main__:action_callback:140 |    IoU: 0.157
2025-08-04 17:22:27 | INFO     | __main__:action_callback:141 |    距离: 46.8px
2025-08-04 17:22:27 | INFO     | __main__:action_callback:142 |    置信度: 0.505
2025-08-04 17:22:27 | INFO     | __main__:action_callback:145 |    保存路径: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_ok/
2025-08-04 17:22:27 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 500: 检测到 4 个对象
2025-08-04 17:22:27 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - hand: 1 个
2025-08-04 17:22:27 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.835, 位置: [107, 674, 291, 818]
2025-08-04 17:22:27 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:22:27 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.617, 位置: [0, 614, 511, 898]
2025-08-04 17:22:27 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:22:27 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.915, 位置: [1168, 223, 1426, 518]
2025-08-04 17:22:27 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:22:27 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.940, 位置: [1105, 508, 1348, 711]
2025-08-04 17:22:30 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 600: 检测到 3 个对象
2025-08-04 17:22:30 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:22:30 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.941, 位置: [0, 584, 493, 898]
2025-08-04 17:22:30 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:22:30 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.910, 位置: [1168, 223, 1426, 518]
2025-08-04 17:22:30 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:22:30 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.948, 位置: [1105, 508, 1348, 711]
2025-08-04 17:22:30 | INFO     | src.state_machine.action_detector:_log_statistics:397 | 统计信息 - 帧数: 600, 运行时间: 21.4s, FPS: 28.0, 状态: idle, 动作数: 1
2025-08-04 17:22:30 | INFO     | src.state_machine.action_detector:_log_statistics:405 | 检测统计 - 有检测的帧: 600/600 (100.0%), 总检测数: 1841, 手: 46, 蓝色箱子: 595, 上层黄色箱子: 600, 下层黄色箱子: 600
2025-08-04 17:22:30 | INFO     | src.state_machine.action_detector:_log_statistics:415 | 性能统计 - 平均推理时间: 14.3ms, 估计FPS: 69.7, 设备: 0
2025-08-04 17:22:30 | INFO     | src.state_machine.action_detector:_log_statistics:420 | GPU内存 - 已分配: 43.5MB, 已缓存: 90.0MB
2025-08-04 17:22:34 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 700: 检测到 3 个对象
2025-08-04 17:22:34 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:22:34 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.932, 位置: [0, 585, 493, 898]
2025-08-04 17:22:34 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:22:34 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.902, 位置: [1168, 224, 1426, 518]
2025-08-04 17:22:34 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:22:34 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.940, 位置: [1104, 508, 1348, 711]
2025-08-04 17:22:37 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 800: 检测到 3 个对象
2025-08-04 17:22:37 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:22:37 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.943, 位置: [0, 585, 493, 898]
2025-08-04 17:22:37 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:22:37 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.906, 位置: [1168, 223, 1426, 518]
2025-08-04 17:22:37 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:22:37 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.941, 位置: [1104, 508, 1348, 711]
2025-08-04 17:22:40 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 900: 检测到 3 个对象
2025-08-04 17:22:40 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:22:40 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.938, 位置: [0, 583, 493, 898]
2025-08-04 17:22:40 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:22:40 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.907, 位置: [1168, 223, 1426, 518]
2025-08-04 17:22:40 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:22:40 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.938, 位置: [1106, 507, 1348, 711]
2025-08-04 17:22:40 | INFO     | src.state_machine.action_detector:_log_statistics:397 | 统计信息 - 帧数: 900, 运行时间: 31.4s, FPS: 28.6, 状态: idle, 动作数: 1
2025-08-04 17:22:40 | INFO     | src.state_machine.action_detector:_log_statistics:405 | 检测统计 - 有检测的帧: 900/900 (100.0%), 总检测数: 2741, 手: 46, 蓝色箱子: 895, 上层黄色箱子: 900, 下层黄色箱子: 900
2025-08-04 17:22:40 | INFO     | src.state_machine.action_detector:_log_statistics:415 | 性能统计 - 平均推理时间: 13.7ms, 估计FPS: 72.9, 设备: 0
2025-08-04 17:22:40 | INFO     | src.state_machine.action_detector:_log_statistics:420 | GPU内存 - 已分配: 43.5MB, 已缓存: 90.0MB
2025-08-04 17:22:41 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与blue_box交互，IoU: 0.109, 距离: 142.7
2025-08-04 17:22:41 | SUCCESS  | src.state_machine.action_state_machine:_handle_detecting_state:155 | 检测到ok_action！第2次动作，持续时间: 0.53秒
2025-08-04 17:22:41 | SUCCESS  | src.state_machine.action_detector:_handle_action_detected:198 | 检测到动作: action_1754299361_2 (类型: ok_action)
2025-08-04 17:22:42 | SUCCESS  | src.utils.video_buffer:save_action_video:117 | 动作视频已保存: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_ok/action_1754299361_2_20250804_172241.mp4 (31帧)
2025-08-04 17:22:42 | SUCCESS  | src.state_machine.action_detector:_handle_action_detected:206 | 动作视频已保存: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_ok/action_1754299361_2_20250804_172241.mp4
2025-08-04 17:22:42 | INFO     | src.state_machine.action_detector:_save_action_image:243 | 动作图像已保存: output/detected_actions/action_1754299361_2_20250804_172241.jpg
2025-08-04 17:22:42 | SUCCESS  | __main__:action_callback:132 | 🎯 检测到动作 #action_1754299361_2
2025-08-04 17:22:42 | INFO     | __main__:action_callback:133 |    动作类型: ✅ OK动作 - 产品放入蓝色箱子
2025-08-04 17:22:42 | INFO     | __main__:action_callback:134 |    容器类型: blue_box
2025-08-04 17:22:42 | INFO     | __main__:action_callback:135 |    时间戳: 17:22:41
2025-08-04 17:22:42 | INFO     | __main__:action_callback:139 |    交互时长: 0.53秒
2025-08-04 17:22:42 | INFO     | __main__:action_callback:140 |    IoU: 0.111
2025-08-04 17:22:42 | INFO     | __main__:action_callback:141 |    距离: 58.6px
2025-08-04 17:22:42 | INFO     | __main__:action_callback:142 |    置信度: 0.733
2025-08-04 17:22:42 | INFO     | __main__:action_callback:145 |    保存路径: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_ok/
2025-08-04 17:22:44 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 1000: 检测到 3 个对象
2025-08-04 17:22:44 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:22:44 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.931, 位置: [0, 586, 494, 898]
2025-08-04 17:22:44 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:22:44 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.912, 位置: [1168, 223, 1426, 518]
2025-08-04 17:22:44 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:22:44 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.944, 位置: [1105, 508, 1348, 711]
2025-08-04 17:22:47 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 1100: 检测到 3 个对象
2025-08-04 17:22:47 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:22:47 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.931, 位置: [0, 583, 493, 898]
2025-08-04 17:22:47 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:22:47 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.895, 位置: [1168, 223, 1426, 518]
2025-08-04 17:22:47 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:22:47 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.937, 位置: [1107, 508, 1348, 711]
2025-08-04 17:22:50 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 1200: 检测到 3 个对象
2025-08-04 17:22:50 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:22:50 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.932, 位置: [0, 583, 493, 898]
2025-08-04 17:22:50 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:22:50 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.900, 位置: [1168, 224, 1426, 518]
2025-08-04 17:22:50 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:22:50 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.951, 位置: [1106, 507, 1348, 710]
2025-08-04 17:22:50 | INFO     | src.state_machine.action_detector:_log_statistics:397 | 统计信息 - 帧数: 1200, 运行时间: 41.6s, FPS: 28.8, 状态: idle, 动作数: 2
2025-08-04 17:22:50 | INFO     | src.state_machine.action_detector:_log_statistics:405 | 检测统计 - 有检测的帧: 1200/1200 (100.0%), 总检测数: 3663, 手: 68, 蓝色箱子: 1195, 上层黄色箱子: 1200, 下层黄色箱子: 1200
2025-08-04 17:22:50 | INFO     | src.state_machine.action_detector:_log_statistics:415 | 性能统计 - 平均推理时间: 13.3ms, 估计FPS: 75.2, 设备: 0
2025-08-04 17:22:50 | INFO     | src.state_machine.action_detector:_log_statistics:420 | GPU内存 - 已分配: 43.5MB, 已缓存: 90.0MB
2025-08-04 17:22:54 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 1300: 检测到 3 个对象
2025-08-04 17:22:54 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:22:54 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.941, 位置: [0, 583, 494, 898]
2025-08-04 17:22:54 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:22:54 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.904, 位置: [1168, 223, 1426, 518]
2025-08-04 17:22:54 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:22:54 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.947, 位置: [1104, 508, 1348, 710]
2025-08-04 17:22:55 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与blue_box交互，IoU: 0.115, 距离: 157.6
2025-08-04 17:22:55 | SUCCESS  | src.state_machine.action_state_machine:_handle_detecting_state:155 | 检测到ok_action！第3次动作，持续时间: 0.50秒
2025-08-04 17:22:55 | SUCCESS  | src.state_machine.action_detector:_handle_action_detected:198 | 检测到动作: action_1754299375_3 (类型: ok_action)
2025-08-04 17:22:55 | SUCCESS  | src.utils.video_buffer:save_action_video:117 | 动作视频已保存: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_ok/action_1754299375_3_20250804_172255.mp4 (31帧)
2025-08-04 17:22:55 | SUCCESS  | src.state_machine.action_detector:_handle_action_detected:206 | 动作视频已保存: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_ok/action_1754299375_3_20250804_172255.mp4
2025-08-04 17:22:55 | INFO     | src.state_machine.action_detector:_save_action_image:243 | 动作图像已保存: output/detected_actions/action_1754299375_3_20250804_172255.jpg
2025-08-04 17:22:55 | SUCCESS  | __main__:action_callback:132 | 🎯 检测到动作 #action_1754299375_3
2025-08-04 17:22:55 | INFO     | __main__:action_callback:133 |    动作类型: ✅ OK动作 - 产品放入蓝色箱子
2025-08-04 17:22:55 | INFO     | __main__:action_callback:134 |    容器类型: blue_box
2025-08-04 17:22:55 | INFO     | __main__:action_callback:135 |    时间戳: 17:22:55
2025-08-04 17:22:55 | INFO     | __main__:action_callback:139 |    交互时长: 0.50秒
2025-08-04 17:22:55 | INFO     | __main__:action_callback:140 |    IoU: 0.126
2025-08-04 17:22:55 | INFO     | __main__:action_callback:141 |    距离: 34.6px
2025-08-04 17:22:55 | INFO     | __main__:action_callback:142 |    置信度: 0.532
2025-08-04 17:22:55 | INFO     | __main__:action_callback:145 |    保存路径: /home/<USER>/wolong_ws/yolo_detect_ws/data/results/action_ok/
2025-08-04 17:22:57 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 1400: 检测到 3 个对象
2025-08-04 17:22:57 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:22:57 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.929, 位置: [0, 584, 493, 898]
2025-08-04 17:22:57 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:22:57 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.909, 位置: [1168, 223, 1426, 518]
2025-08-04 17:22:57 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:22:57 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.943, 位置: [1103, 507, 1348, 711]
2025-08-04 17:23:00 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与blue_box交互，IoU: 0.161, 距离: 137.0
2025-08-04 17:23:01 | INFO     | src.state_machine.action_state_machine:_handle_detecting_state:130 | 手部与blue_box的交互中断
2025-08-04 17:23:01 | INFO     | src.state_machine.action_detector:_log_detection_results:374 | 帧 1500: 检测到 4 个对象
2025-08-04 17:23:01 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - hand: 1 个
2025-08-04 17:23:01 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.503, 位置: [196, 650, 328, 816]
2025-08-04 17:23:01 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - blue_box: 1 个
2025-08-04 17:23:01 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.928, 位置: [4, 599, 471, 898]
2025-08-04 17:23:01 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - upper_yellow_box: 1 个
2025-08-04 17:23:01 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.907, 位置: [1168, 224, 1426, 518]
2025-08-04 17:23:01 | INFO     | src.state_machine.action_detector:_log_detection_results:377 |   - lower_yellow_box: 1 个
2025-08-04 17:23:01 | INFO     | src.state_machine.action_detector:_log_detection_results:381 |     [1] 置信度: 0.939, 位置: [1105, 508, 1348, 711]
2025-08-04 17:23:01 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与blue_box交互，IoU: 0.157, 距离: 29.1
2025-08-04 17:23:01 | INFO     | src.state_machine.action_detector:_log_statistics:397 | 统计信息 - 帧数: 1500, 运行时间: 51.8s, FPS: 28.9, 状态: detecting, 动作数: 3
2025-08-04 17:23:01 | INFO     | src.state_machine.action_detector:_log_statistics:405 | 检测统计 - 有检测的帧: 1500/1500 (100.0%), 总检测数: 4603, 手: 108, 蓝色箱子: 1495, 上层黄色箱子: 1500, 下层黄色箱子: 1500
2025-08-04 17:23:01 | INFO     | src.state_machine.action_detector:_log_statistics:415 | 性能统计 - 平均推理时间: 13.1ms, 估计FPS: 76.2, 设备: 0
2025-08-04 17:23:01 | INFO     | src.state_machine.action_detector:_log_statistics:420 | GPU内存 - 已分配: 43.5MB, 已缓存: 90.0MB
2025-08-04 17:23:01 | INFO     | src.state_machine.action_state_machine:_handle_detecting_state:130 | 手部与blue_box的交互中断
2025-08-04 17:23:03 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与lower_yellow_box交互，IoU: 0.212, 距离: 100.1
2025-08-04 17:23:03 | INFO     | src.state_machine.action_state_machine:_handle_detecting_state:130 | 手部与lower_yellow_box的交互中断
2025-08-04 17:23:03 | INFO     | src.state_machine.action_state_machine:_handle_idle_state:114 | 检测到手部与lower_yellow_box交互，IoU: 0.175, 距离: 103.9
